<?php
namespace app\manage\controller;

use app\common\controller\BaseController;
use think\App;
use think\Request;
use think\Exception;
use think\facade\Cache;
use think\facade\Validate;
use think\facade\View;
use think\facade\Db;
use app\manage\model\User as User;
use app\manage\model\UserAdmin as UserAdmin;
use app\manage\model\Liar as Liar;
use app\manage\controller\manage\Profile as Profile;
use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception as MailException;
use app\common\helper\Mail as Mail;

class Manage extends BaseController 
{
    public function __construct(App $app, Request $request) {
        parent::__construct($app, $request);
    }

    public function Index() {
        if($this->request->isPost()) {
            // 处理POST请求
            $action = $this->request->param('action', '');

            switch($action) {
                case 'register':
                    return $this->handleRegister();
                case 'sendMail':
                    return $this->handleSendMail();
                case 'forgotPassword':
                    return $this->handleForgotPassword();
                case 'resetPassword':
                    return $this->handleResetPassword();
                case 'sendCode':
                    return $this->handleSendCode();
                default:
                    return json(['code' => 0, 'msg' => '未知操作']);
            }
        }

        return View::fetch('index');
    }

    public function ChangeLog() {
        // 获取管理员菜单项
        $adminMenuItems = [];
        if (isset($_SESSION['admin']) && $_SESSION['admin']) {
            $adminMenuItems = UserAdmin::GetItems();
        }

        return View::fetch('changelog', [
            'adminMenuItems' => $adminMenuItems
        ]);
    }

    public function Choose() {
        return View::fetch('choose');
    }

    public function Help() {
        return View::fetch('help');
    }

    // 骗子列表
    public function Liars() {
        return View::fetch('liars', [
            'data' => Liar::Gets()->paginate(20)
        ]);
    }

    // 提交骗子
    public function LiarPost() {
        return View::fetch('liarpost', [
            'liar_info' => ['id'=>1,'server'=>null,'area_name'=>null,'type'=>null]
        ]);
    }

    public function Login() {
        // 判断是否已经登录
        $admin = $this->request->get('type') == 'admin';
        if (!$admin) return redirect('http://www.bnszs.com');
        //if (isset($_SESSION['user']) && !$admin) return redirect('/manage/center');
        if (isset($_SESSION['admin']) && $admin) return redirect('/manage');
  

        if ($this->request->isPost()) {
            try {
                $data = $this->request->post();
                
                // 验证数据
                $validate = validate([
                    'username' => 'require|length:3,20',
                    'password' => 'require|length:5,32'
                ]);

                if (!$validate->check($data)) {
                    throw new Exception($validate->getError());
                }

                if ($admin) {
                    // 管理员登录
                    try {
                        $user = UserAdmin::login($data['username'], $data['password']);
                        if ($user) {
                            $_SESSION['admin'] = $user['uid']; // 直接使用原生PHP session

                            // 验证session是否设置成功
                            $verifySession = $_SESSION['admin'] ?? null;

                            return $this->json([
                                'code' => 0,
                                'msg' => '登录成功',
                                'url' => '/manage',
                                'debug' => [
                                    'user_uid' => $user['uid'],
                                    'session_set' => $verifySession,
                                    'session_match' => $verifySession == $user['uid']
                                ]
                            ]);
                        } else {
                            throw new Exception('用户名或密码错误');
                        }
                    } catch (\Exception $e) {
                        return $this->json([
                            'code' => 1,
                            'msg' => '登录失败: ' . $e->getMessage(),
                            'debug' => [
                                'username' => $data['username'],
                                'error_type' => get_class($e)
                            ]
                        ]);
                    }
                } else {
                    // 普通用户登录
                    $user = User::login($data['username'], $data['password']);
                    if ($user) {
                        session('user', $user);
                        return $this->json(['code' => 0, 'msg' => '登录成功', 'url' => '/manage/center']);
                    } else {
                        throw new Exception('用户名或密码错误');
                    }
                }
                
            } catch (Exception $e) {
                return $this->json(['code' => 1, 'msg' => $e->getMessage()]);
            }
        }

        return View::fetch('login', [
            'admin' => $admin
        ]);
    }





    public function Register() {
        if ($this->request->isPost()) {
            return $this->handleRegister();
        }

        return View::fetch('register');
    }

    /**
     * 处理注册（私有方法）
     */
    private function handleRegister() {
        try {
            $data = $this->request->post();

            // 验证数据
            $validate = Validate::make([
                'username' => 'require|length:3,20|unique:user',
                'password' => 'require|length:6,32',
                'email' => 'require|email|unique:user'
            ]);

            if (!$validate->check($data)) {
                throw new Exception($validate->getError());
            }

            // 注册用户
            $user = User::register($data);
            if ($user) {
                return $this->json(['code' => 0, 'msg' => '注册成功']);
            } else {
                throw new Exception('注册失败');
            }

        } catch (Exception $e) {
            return $this->json(['code' => 1, 'msg' => $e->getMessage()]);
        }
    }

    public function Logout() {
        unset($_SESSION['user']);
        unset($_SESSION['admin']);
        return redirect('/manage/login');
    }

    public function Center() {
        if (!session('user')) {
            return redirect('/manage/login');
        }

        if($this->request->isPost()) {
            // 处理用户中心的POST请求
            $action = $this->request->param('action', '');

            switch($action) {
                case 'updateProfile':
                    return $this->handleUpdateProfile();
                case 'changePassword':
                    return $this->handleChangePassword();
                case 'uploadAvatar':
                    return $this->handleUploadAvatar();
                default:
                    return json(['code' => 0, 'msg' => '未知操作']);
            }
        }

        $user = session('user');
        return View::fetch('center', [
            'user' => $user
        ]);
    }

    /**
     * 处理更新用户资料（私有方法）
     */
    private function handleUpdateProfile() {
        try {
            $data = $this->request->post();
            // 更新用户资料逻辑
            return json(['code' => 1, 'msg' => '更新成功']);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '更新失败：' . $e->getMessage()]);
        }
    }

    /**
     * 处理修改密码（私有方法）
     */
    private function handleChangePassword() {
        try {
            $data = $this->request->post();
            // 修改密码逻辑
            return json(['code' => 1, 'msg' => '密码修改成功']);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '密码修改失败：' . $e->getMessage()]);
        }
    }

    /**
     * 处理上传头像（私有方法）
     */
    private function handleUploadAvatar() {
        try {
            // 上传头像逻辑
            return json(['code' => 1, 'msg' => '头像上传成功']);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '头像上传失败：' . $e->getMessage()]);
        }
    }

    public function Admin() {
        // 重定向到index方法，保持与TP5.1一致的行为
        return redirect('/manage');
    }



    /**
     * 检查数据库表数据
     */
    public function checkDatabase() {
        $result = [];

        try {
            // 检查管理员表
            $admins = Db::query("SELECT uid, username, isAction, power FROM bns_useradmin LIMIT 5");
            $result['admins'] = $admins;

            // 检查菜单项表
            $menuItems = Db::query("SELECT id, itemName, url, icon, isAction, isNavigation, sort FROM bns_useradminitem ORDER BY sort DESC LIMIT 10");
            $result['menu_items'] = $menuItems;

            // 检查表是否存在
            $tables = Db::query("SHOW TABLES LIKE 'bns_%'");
            $result['tables'] = $tables;

        } catch (\Exception $e) {
            $result['error'] = $e->getMessage();
        }

        return json($result);
    }



    /**
     * 发送邮件（私有方法）
     */
    private function handleSendMail() {
        if ($this->request->isPost()) {
            try {
                $data = $this->request->post();
                
                $validate = Validate::make([
                    'to' => 'require|email',
                    'subject' => 'require',
                    'content' => 'require'
                ]);
                
                if (!$validate->check($data)) {
                    throw new Exception($validate->getError());
                }

                $result = Mail::send($data['to'], $data['subject'], $data['content']);
                
                if ($result) {
                    return $this->json(['code' => 0, 'msg' => '邮件发送成功']);
                } else {
                    throw new Exception('邮件发送失败');
                }
                
            } catch (Exception $e) {
                return $this->json(['code' => 1, 'msg' => $e->getMessage()]);
            }
        }
        
        return View::fetch('sendmail');
    }

    /**
     * 找回密码（私有方法）
     */
    private function handleForgotPassword() {
        if ($this->request->isPost()) {
            try {
                $email = $this->request->post('email');
                
                if (!$email) {
                    throw new Exception('请输入邮箱地址');
                }
                
                $user = User::getByEmail($email);
                if (!$user) {
                    throw new Exception('邮箱地址不存在');
                }
                
                // 生成重置令牌
                $token = md5($email . time() . rand(1000, 9999));
                
                // 保存令牌到缓存，有效期30分钟
                Cache::set('reset_password_' . $token, $user['id'], 1800);
                
                // 发送重置邮件
                $resetUrl = request()->domain() . '/manage/resetPassword?token=' . $token;
                $content = "请点击以下链接重置密码（30分钟内有效）：\n" . $resetUrl;
                
                $result = Mail::send($email, '密码重置', $content);
                
                if ($result) {
                    return $this->json(['code' => 0, 'msg' => '重置邮件已发送']);
                } else {
                    throw new Exception('邮件发送失败');
                }
                
            } catch (Exception $e) {
                return $this->json(['code' => 1, 'msg' => $e->getMessage()]);
            }
        }
        
        return View::fetch('forgot_password');
    }

    /**
     * 重置密码（私有方法）
     */
    private function handleResetPassword() {
        $token = $this->request->get('token');
        
        if (!$token) {
            abort(404);
        }
        
        $userId = Cache::get('reset_password_' . $token);
        if (!$userId) {
            abort(404, '重置链接已过期');
        }
        
        if ($this->request->isPost()) {
            try {
                $password = $this->request->post('password');
                $confirmPassword = $this->request->post('confirm_password');
                
                if (!$password || strlen($password) < 6) {
                    throw new Exception('密码长度不能少于6位');
                }
                
                if ($password !== $confirmPassword) {
                    throw new Exception('两次输入的密码不一致');
                }
                
                // 更新密码
                $result = User::updatePassword($userId, $password);
                
                if ($result) {
                    // 删除重置令牌
                    Cache::delete('reset_password_' . $token);
                    return $this->json(['code' => 0, 'msg' => '密码重置成功']);
                } else {
                    throw new Exception('密码重置失败');
                }
                
            } catch (Exception $e) {
                return $this->json(['code' => 1, 'msg' => $e->getMessage()]);
            }
        }
        
        return View::fetch('reset_password', [
            'token' => $token
        ]);
    }


}
