{include file="manage/template" /}

<div class="admin-content">
	<div class="admin-content-body">
		<div class="am-cf am-padding">
			<div class="am-fl am-cf"><strong class="am-text-primary am-text-lg">首页</strong> / <small>CDkey下载管理</small></div>
		</div>
		
		<div class="am-g">
			<div class="am-u-sm-12">
				<div class="am-panel am-panel-default">
					<div class="am-panel-hd">CDkey文件下载</div>
					<div class="am-panel-bd">
						{if condition="$files"}
						<table class="am-table am-table-bd am-table-striped admin-content-table">
							<thead>
								<tr>
									<th>文件名</th>
									<th>文件大小</th>
									<th>生成时间</th>
									<th>操作</th>
								</tr>
							</thead>
							<tbody>
							{volist name="files" id="file"}
								<tr>
									<td>{$file.name}</td>
									<td>{$file.size|format_bytes}</td>
									<td>{$file.time}</td>
									<td>
										<div class="am-btn-group am-btn-group-xs">
											<a href="{$file.url}" class="am-btn am-btn-primary am-btn-xs" download>
												<span class="am-icon-download"></span> 下载
											</a>
											<button class="am-btn am-btn-danger am-btn-xs" onclick="deleteFile('{$file.name}')">
												<span class="am-icon-trash"></span> 删除
											</button>
										</div>
									</td>
								</tr>
							{/volist}
							</tbody>
						</table>
						{else /}
						<div class="am-alert am-alert-warning">
							<p>暂无CDkey文件可下载</p>
						</div>
						{/if}
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<script>
function deleteFile(filename) {
	if (!confirm('确定要删除文件 ' + filename + ' 吗？')) {
		return;
	}
	
	$.ajax({
		type: "POST",
		dataType: "json",
		url: "/manage/admin/deleteDownload",
		data: {filename: filename},
		success: function(result) {
			alert(result.msg);
			if (result.code) {
				location.reload();
			}
		},
		error: function() {
			alert("删除失败，未知错误");
		}
	});
}

// 格式化文件大小的JavaScript函数
function formatBytes(bytes) {
	if (bytes === 0) return '0 Bytes';
	const k = 1024;
	const sizes = ['Bytes', 'KB', 'MB', 'GB'];
	const i = Math.floor(Math.log(bytes) / Math.log(k));
	return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 页面加载完成后格式化文件大小
$(document).ready(function() {
	$('td').each(function() {
		var text = $(this).text();
		if (/^\d+$/.test(text) && parseInt(text) > 1000) {
			$(this).text(formatBytes(parseInt(text)));
		}
	});
});
</script>
