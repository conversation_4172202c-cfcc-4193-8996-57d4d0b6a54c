<?php

namespace app\manage\controller;
use app\common\controller\BaseController;
use think\App;
use think\Request;
use think\facade\Db;
use think\Exception;
use think\facade\Cache;
use think\facade\View;
use app\manage\model\CDkey;
use app\manage\model\User;
use app\manage\model\Profile as ProfileModel;

class Admin extends BaseController
{
    private $admin = 0;
    protected $request;

    function __construct(App $app, Request $request) {
        parent::__construct($app, $request);
        $this->request = $request;

        $admin = $this->admin = $_SESSION['admin'] ?? null;
        if($admin == 0 || $admin == null) {
            header("Location:/manage/login?type=admin&callback=/manage");
            die;
        }
    }

    /**
     * 管理员功能统一入口
     */
    public function check() {
        if($this->request->isPost()) {
            // 处理POST请求 - 暂时不支持POST操作
            return json(['code' => 0, 'msg' => 'POST操作暂不支持']);
        }

        // 默认显示管理员检查页面
        $status = $this->request->param('status', 0);
        $permission = $this->request->param('permission', 0);

        return View::fetch('admin/check', [
            'current_status' => $status,
            'current_permission' => $permission
        ]);
    }

    /**
     * 用户管理页面
     */
    public function users() {
        if($this->request->isPost()) {
            try {
                $mode = $this->request->param('mode');

                switch ($mode) {
                    case "cdkey":
                        CDkey::Activate($this->request->param('data'), $this->request->param('uid'), $this->admin);
                        break;

                    case "register":
                        User::AddWeb($this->request->param('uin'), NULL, $this->admin);
                        break;

                    case "device":
                        $uid = intval($this->request->param('uid'));
                        if ($uid) {
                            // TP5.1兼容的Redis连接方式
                            $redis = new \Redis();
                            $config = Config::get('cache');
                            $redis->connect($config['host'], $config['port']);
                            if (!empty($config['password'])) {
                                $redis->auth($config['password']);
                            }
                            $redis->select($config['select']);
                            $redis->hDel("user_device_time", $uid);
                            $redis->close();
                        }
                        break;

                    case "updateStatus":
                        $uid = intval($this->request->param('uid'));
                        $status = intval($this->request->param('status'));
                        $reason = $this->request->param('reason', '');

                        if ($uid && isset($status)) {
                            $user = User::where('uid', $uid)->find();
                            if (!$user) {
                                throw new Exception('用户不存在');
                            }

                            $user->status = $status;
                            $user->save();

                            // 记录操作日志
                            User::AddLog($uid, 'status_change', "管理员修改状态: {$status}, 原因: {$reason}", $this->admin);
                        }
                        break;

                    case "updatePermission":
                        $uid = intval($this->request->param('uid'));
                        $permission = intval($this->request->param('permission'));
                        $reason = $this->request->param('reason', '');

                        if ($uid && isset($permission)) {
                            $user = User::where('uid', $uid)->find();
                            if (!$user) {
                                throw new Exception('用户不存在');
                            }

                            $user->permission = $permission;
                            $user->save();

                            // 记录操作日志
                            User::AddLog($uid, 'permission_change', "管理员修改权限: {$permission}, 原因: {$reason}", $this->admin);
                        }
                        break;

                    case "batchUpdateStatus":
                        $uids = $this->request->param('uids');
                        $status = intval($this->request->param('status'));
                        $reason = $this->request->param('reason', '');

                        if ($uids && isset($status)) {
                            $uidArray = is_array($uids) ? $uids : explode(',', $uids);
                            foreach ($uidArray as $uid) {
                                $uid = intval($uid);
                                if ($uid) {
                                    $user = User::where('uid', $uid)->find();
                                    if ($user) {
                                        $user->status = $status;
                                        $user->save();

                                        // 记录操作日志
                                        User::AddLog($uid, 'batch_status_change', "管理员批量修改状态: {$status}, 原因: {$reason}", $this->admin);
                                    }
                                }
                            }
                        }
                        break;

                    case "clearCache":
                        $uid = intval($this->request->param('uid'));
                        if ($uid) {
                            try {
                                $redis = Cache::store('redis')->handler();
                                // 清除用户相关的所有缓存
                                $patterns = [
                                    "user_expir_*",
                                    "user_draw_{$uid}_*",
                                    "device_signin_*",
                                    "signin_unavailable_{$uid}_*"
                                ];

                                $clearedCount = 0;
                                foreach ($patterns as $pattern) {
                                    $keys = $redis->keys($pattern);
                                    if (is_array($keys) && !empty($keys)) {
                                        foreach ($keys as $key) {
                                            if ($redis->del($key)) {
                                                $clearedCount++;
                                            }
                                        }
                                    }
                                }

                                // 清除用户设备绑定
                                if ($redis->hDel("user_device_time", $uid)) {
                                    $clearedCount++;
                                }

                                // 记录操作日志
                                User::AddLog($uid, 'clear_cache', "管理员清除用户缓存，清理了{$clearedCount}个缓存项", $this->admin);
                            } catch (Exception $e) {
                                throw new Exception("清除缓存失败: " . $e->getMessage());
                            }
                        }
                        break;

                    default: return;
                }

                return json(['code'=>1, 'msg'=>'操作成功']);
            }
            catch(\Exception $e) {
                return json(['code'=>0, 'msg'=>$e->getMessage()]);
            }
        }

        // 筛选条件
        $search = $this->request->get('search');
        $status = $this->request->get('status');
        $permission = $this->request->get('permission');

        $data = User::WHERE(function($query) use ($search, $status, $permission) {
            if($search) {
                $query->where('uin', $search)
                      ->whereOr('uid', $search)
                      ->whereOr('name', 'like', "%{$search}%");
            }
            if(isset($status) && $status !== '') {
                $query->where('status', $status);
            }
            if(isset($permission) && $permission !== '') {
                $query->where('permission', $permission);
            }
        })->order('uid', 'desc')->paginate(20);

        // 获取统计信息
        $stats = [
            'total' => User::count(),
            'normal' => User::where('status', 0)->count(),
            'banned' => User::where('status', '>', 0)->count(),
            'premium' => User::where('permission', '>', 0)->count(),
        ];

        return View::fetch('/admin/users', [
            'data' => $data,
            'stats' => $stats,
            'current_search' => $search,
            'current_status' => $status,
            'current_permission' => $permission
        ]);
    }

    public function cdkey() {
        $admin = $_SESSION['admin'] ?? null;

        // 创建日志目录
        $logBaseDir = \think\facade\App::getRuntimePath() . 'log/cdkey_logs';
        $year = date('Y');
        $month = date('m');
        $logDir = $logBaseDir . '/' . $year . '/' . $month;

        // 确保目录存在并可写
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }

        // 日志文件名
        $logFile = $logDir . '/' . date('Y-m-d') . '.log';

        // CDkey记录文件目录
        $cdkeyRecordDir = $logBaseDir . '/cdkey_records/' . $year . '/' . $month;
        if (!is_dir($cdkeyRecordDir)) {
            mkdir($cdkeyRecordDir, 0755, true);
        }

        try {

            if($_POST) {
                $logContent = "[" . date('Y-m-d H:i:s') . "] =================== 开始生成CDKey ===================\n";
                $logContent .= "[" . date('Y-m-d H:i:s') . "] 管理员ID: " . $admin . "\n";

                try {
                    $usableTimes = $_POST['usableTimes'] ?? '';
                    $changeTimes = $_POST['changeTimes'] ?? '';
                    $startTime =  $_POST['startTime'] ?? '';
                    $endTime = $_POST['endTime'] ?? '';
                    $batch = $_POST['batch'] ?? null;
                    $num = $_POST['num'] ?? '';
                    $discern = $_POST['discern'] ?? '';
                    $remark = $_POST['remark'] ?? '';
                    $type = $_POST['type'] ?? '';
                    $SpecifiedValue = $_POST['specified-value'] ?? '';

                    // 记录参数
                    $logContent .= "[" . date('Y-m-d H:i:s') . "] 参数信息:\n";
                    $logContent .= "使用次数: {$usableTimes}\n";
                    $logContent .= "修改次数: {$changeTimes}\n";
                    $logContent .= "开始时间: {$startTime}\n";
                    $logContent .= "结束时间: {$endTime}\n";
                    $logContent .= "批次: {$batch}\n";
                    $logContent .= "生成数量: {$num}\n";
                    $logContent .= "识别码: {$discern}\n";
                    $logContent .= "备注: {$remark}\n";
                    $logContent .= "类型: {$type}\n";
                    $logContent .= "指定值: {$SpecifiedValue}\n";

                    // 处理时间数据
                    if ($type == "customize" || $type == "client") {
                        $timeType  = $_POST['time-limit-type'] ?? '';
                        $duration  = $_POST['usable-duration'] ?? '';
                        $fixedTime = $_POST['fixed-expiration-time'] ?? '';

                        $logContent .= "时间类型: {$timeType}\n";
                        $logContent .= "持续时间: {$duration}\n";
                        $logContent .= "固定过期时间: {$fixedTime}\n";

                        if(!strtotime($fixedTime)) $fixedTime = null;

                        switch ($timeType) {
                            case 'fixed':
                                break;

                            case 'duration':
                                if ($duration <= 0) {
                                    $logContent .= "[" . date('Y-m-d H:i:s') . "] 错误: 时间天数必须大于 0\n";
                                    throw new Exception("时间天数必须大于 0");
                                }
                                break;

                            default:
                                $logContent .= "[" . date('Y-m-d H:i:s') . "] 错误: 无效的时间类型\n";
                                throw new Exception("无效的时间类型");
                        }
                    }

                    // 批量生成时强制设置可使用次数为1
                    $usableTimes = 1;

                    //最大修改次数处理
                    if (empty($changeTimes)) $changeTimes = 3;
                    else if ($changeTimes == '∞') $changeTimes = 'null';
                    else $changeTimes = min(9999, intval($changeTimes));

                    // 批量生成时不使用批次信息，设为null
                    $batch = null;

                    if (empty($num)) $num = 1;
                    else if ($num > 5000) $num = 5000;
                    else $num = intval($num);

                    $CDkeyMsg = '';
                    $generatedCDkeys = []; // 存储生成的CDkey列表

                    for ($n = 0; $n < $num; $n++) {
                        $CDkey = CDkey::Random($discern, $n);
                        if (empty($CDkey)) {
                            $logContent .= "[" . date('Y-m-d H:i:s') . "] 警告: 第{$n}个CDkey生成为空\n";
                            continue;
                        }

                        $logContent .= "[" . date('Y-m-d H:i:s') . "] 开始插入第{$n}个CDkey: {$CDkey}\n";

                        Db::startTrans();
                        try {
                            // 记录主表数据
                            $mainData = [
                                'cdkey'=>$CDkey,
                                'type'=>$type,
                                'MaxNum'=>$usableTimes,
                                'RemainNum'=>$usableTimes,
                                'reason'=>$remark,
                                'batch'=>$batch,
                                'admin'=>$admin,
                                'startTime'=> strtotime($startTime) ? $startTime : Date("Y-m-d H:i:s"),
                                'endTime'  => strtotime($endTime) ? $endTime : null,
                            ];
                            $logContent .= "[" . date('Y-m-d H:i:s') . "] 主表数据: " . json_encode($mainData, JSON_UNESCAPED_UNICODE) . "\n";

                            // 插入主表
                            $mainResult = Db::name('bns_cdkey')->insert($mainData);
                            $logContent .= "[" . date('Y-m-d H:i:s') . "] 主表插入" . ($mainResult ? '成功' : '失败') . "\n";

                            // 子表数据准备
                            switch($type) {
                                case "client":
                                case "customize":
                                    $subData = [
                                        'cdkey'=>$CDkey,
                                        'timeType'=>$timeType,
                                        'fixed'=>$fixedTime,
                                        'duration'=>$duration,
                                        'modifyNum'=>$changeTimes
                                    ];
                                    $logContent .= "[" . date('Y-m-d H:i:s') . "] customize子表数据: " . json_encode($subData, JSON_UNESCAPED_UNICODE) . "\n";
                                    $subResult = Db::name('bns_cdkey_customize')->insert($subData);
                                    break;

                                case "drawtimes":
                                    $subData = [
                                        'cdkey'=>$CDkey,
                                        'schedule'=>null,
                                        'max_times'=>$SpecifiedValue,
                                        'cycleType'=>'none'
                                    ];
                                    $logContent .= "[" . date('Y-m-d H:i:s') . "] drawtimes子表数据: " . json_encode($subData, JSON_UNESCAPED_UNICODE) . "\n";
                                    $subResult = Db::name('bns_cdkey_drawtimes')->insert($subData);
                                    break;

                                default:
                                    throw new Exception("缺少子表处理");
                            }

                            $logContent .= "[" . date('Y-m-d H:i:s') . "] 子表插入" . ($subResult ? '成功' : '失败') . "\n";

                            Db::commit();
                            $logContent .= "[" . date('Y-m-d H:i:s') . "] 事务提交成功\n";

                            $CDkeyMsg .= $CDkey . "\n";
                            $generatedCDkeys[] = $CDkey; // 添加到生成列表

                        } catch (\Exception $e) {
                            Db::rollback();
                            $logContent .= "[" . date('Y-m-d H:i:s') . "] 数据库错误: " . $e->getMessage() . "\n";
                            $logContent .= "[" . date('Y-m-d H:i:s') . "] SQL: " . Db::getLastSql() . "\n";
                            $logContent .= "[" . date('Y-m-d H:i:s') . "] 堆栈: " . $e->getTraceAsString() . "\n";
                            throw $e;
                        }
                    }

                    $logContent .= "[" . date('Y-m-d H:i:s') . "] CDKey生成成功，共生成{$num}个\n";
                    $logContent .= "[" . date('Y-m-d H:i:s') . "] =================== 生成结束 ===================\n\n";
                    file_put_contents($logFile, $logContent, FILE_APPEND);

                    // 创建可下载的CDkey文件
                    $downloadDir = \think\facade\App::getRootPath() . 'public/downloads/cdkeys';
                    if (!is_dir($downloadDir)) {
                        mkdir($downloadDir, 0755, true);
                    }

                    $timestamp = date('Y-m-d_H-i-s');
                    $filename = "cdkeys_{$discern}_{$timestamp}.txt";
                    $downloadFile = $downloadDir . '/' . $filename;

                    // 创建文件内容
                    $fileContent = "# CDkey批量生成记录\n";
                    $fileContent .= "# 生成时间: " . date('Y-m-d H:i:s') . "\n";
                    $fileContent .= "# 生成管理员: {$admin}\n";
                    $fileContent .= "# 识别前缀: {$discern}\n";
                    $fileContent .= "# 类型: {$type}\n";
                    $fileContent .= "# 可使用次数: 1\n";
                    $fileContent .= "# 期限类型: " . ($timeType == 'duration' ? '时长模式' : '固定时间模式') . "\n";
                    if ($timeType == 'duration') {
                        $fileContent .= "# 有效时长: {$duration}天\n";
                    } else {
                        $fileContent .= "# 固定到期时间: {$fixedTime}\n";
                    }
                    $fileContent .= "# 激活开始时间: {$startTime}\n";
                    $fileContent .= "# 激活结束时间: {$endTime}\n";
                    $fileContent .= "# 备注: {$remark}\n";
                    $fileContent .= "# 总数量: {$num}\n";
                    $fileContent .= "# ==========================================\n\n";

                    foreach ($generatedCDkeys as $cdkey) {
                        $fileContent .= $cdkey . "\n";
                    }

                    file_put_contents($downloadFile, $fileContent);

                    $downloadUrl = '/downloads/cdkeys/' . $filename;

                    return json([
                        'code' => true,
                        'msg' => "生成完毕，共生成" . $num . "个CDkey",
                        'download_url' => $downloadUrl,
                        'filename' => $filename,
                        'cdkeys' => $CDkeyMsg
                    ]);

                } catch (\Exception $e) {
                    $logContent .= "[" . date('Y-m-d H:i:s') . "] 错误: " . $e->getMessage() . "\n";
                    $logContent .= "[" . date('Y-m-d H:i:s') . "] =================== 生成失败 ===================\n\n";
                    file_put_contents($logFile, $logContent, FILE_APPEND);

                    return json(['code'=>false,'msg'=>$e->getMessage()]);
                }
            }

            // 筛选条件
            $search = $this->request->get('search');
            $showAll = $this->request->get('all', 'false') === 'true';
            $mode = $this->request->get('mode', '0');

            // 构建查询条件
            $where = [];

            // 如果不是查看所有，只显示当前管理员的CDKey
            if (!$showAll) {
                $where[] = ['admin', '=', $admin];
            }

            // 根据模式筛选
            switch ($mode) {
                case '1': // 当前可用
                    $where[] = ['RemainNum', '>', 0];
                    break;
                case '2': // 当前不可用
                    $where[] = ['RemainNum', '<=', 0];
                    break;
                default: // 全部
                    break;
            }

            // 搜索条件
            if (!empty($search)) {
                $where[] = ['cdkey|reason|type', 'like', '%' . $search . '%'];
            }

            // 使用关联查询获取管理员用户名
            $data = Db::name('bns_cdkey')
                ->alias('c')
                ->leftJoin('bns_useradmin a', 'c.admin = a.uid')
                ->field('c.*, a.username')
                ->where($where)
                ->order('c.id', 'desc')
                ->paginate(30);

            return View::fetch('/admin/cdkey', [
                'data' => $data
            ]);

        } catch (\Exception $e) {
            // 记录未预期的错误
            $logContent = "[" . date('Y-m-d H:i:s') . "] =================== 未预期错误 ===================\n";
            $logContent .= "[" . date('Y-m-d H:i:s') . "] 错误: " . $e->getMessage() . "\n";
            $logContent .= "[" . date('Y-m-d H:i:s') . "] 文件: " . $e->getFile() . "\n";
            $logContent .= "[" . date('Y-m-d H:i:s') . "] 行号: " . $e->getLine() . "\n";
            $logContent .= "[" . date('Y-m-d H:i:s') . "] 堆栈跟踪: " . $e->getTraceAsString() . "\n";
            $logContent .= "[" . date('Y-m-d H:i:s') . "] =================== 错误结束 ===================\n\n";

            try {
                file_put_contents($logFile, $logContent, FILE_APPEND);
            } catch (\Exception $logException) {
                // 如果日志写入失败，直接返回错误信息
                return json(['code'=>false,'msg'=>'系统错误: ' . $e->getMessage()]);
            }

            return json(['code'=>false,'msg'=>'系统错误，请查看日志: ' . $e->getMessage()]);
        }
    }

    public function profiles() {
        // 筛选条件
        $mode = (integer)$this->request->get('mode', 0);
        $search = $this->request->get('search');
        $data = ProfileModel::WHERE(function($query) use ($mode,$search) {
            switch($mode) {
                case 1: $query->where('status',0); break;
                case 2: $query->where('status',2); break;
            }
        })->order('id', 'desc')->paginate(30);

        return View::fetch('/admin/profiles', [
            'data' => $data
        ]);
    }

    /**
     * CDkey下载管理
     */
    public function downloads() {
        $downloadDir = \think\facade\App::getRootPath() . 'public/downloads/cdkeys';
        $files = [];

        if (is_dir($downloadDir)) {
            $fileList = scandir($downloadDir);
            foreach ($fileList as $file) {
                if ($file != '.' && $file != '..' && pathinfo($file, PATHINFO_EXTENSION) == 'txt') {
                    $filePath = $downloadDir . '/' . $file;
                    $files[] = [
                        'name' => $file,
                        'size' => filesize($filePath),
                        'time' => date('Y-m-d H:i:s', filemtime($filePath)),
                        'url' => '/downloads/cdkeys/' . $file
                    ];
                }
            }

            // 按时间倒序排列
            usort($files, function($a, $b) {
                return strtotime($b['time']) - strtotime($a['time']);
            });
        }

        return View::fetch('/admin/downloads', [
            'files' => $files
        ]);
    }

    /**
     * 删除CDkey下载文件
     */
    public function deleteDownload() {
        if (!$this->request->isPost()) {
            return json(['code' => false, 'msg' => '请求方式错误']);
        }

        $filename = $this->request->param('filename');
        if (empty($filename)) {
            return json(['code' => false, 'msg' => '文件名不能为空']);
        }

        // 安全检查：只允许删除txt文件，且文件名不能包含路径分隔符
        if (pathinfo($filename, PATHINFO_EXTENSION) != 'txt' ||
            strpos($filename, '/') !== false ||
            strpos($filename, '\\') !== false) {
            return json(['code' => false, 'msg' => '非法文件名']);
        }

        $downloadDir = \think\facade\App::getRootPath() . 'public/downloads/cdkeys';
        $filePath = $downloadDir . '/' . $filename;

        if (!file_exists($filePath)) {
            return json(['code' => false, 'msg' => '文件不存在']);
        }

        if (unlink($filePath)) {
            return json(['code' => true, 'msg' => '文件删除成功']);
        } else {
            return json(['code' => false, 'msg' => '文件删除失败']);
        }
    }
}
