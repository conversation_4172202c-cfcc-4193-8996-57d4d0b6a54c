﻿using System.Diagnostics;
using System.Windows;
using Xylia.BnsHelper.Services.Network.BinaryProtocol;
using Xylia.Preview.Data.Engine;

namespace Xylia.BnsHelper.Services.Network.Service;
internal sealed class BnszsSession : EventSession
{
    #region Fields
    public string? SessionToken { get; private set; }

    /// <summary>
    /// 心跳间隔时间（毫秒）
    /// 默认：25分钟
    /// </summary>
    public const int HeartbeatInterval = 25 * 60 * 1000;
    private volatile bool _heartbeatRunning = false;
    private Thread? _heartbeatThread;

    public event EventHandler? HeartbeatFailed;
    #endregion

    #region Constructor
#if DEBUG 
    public BnszsSession() : base("127.0.0.1", 8081) { }
#else
    public BnszsSession() : base("tools.bnszs.com", 8081) { }
#endif

    protected override byte[] XorKey { get; } = [92, 62, 100, 99, 255, 94, 254, 99, 33, 8, 246, 154, 15, 194, 179, 148, 252, 85, 170, 16];
    #endregion

    #region Methods
    protected override IPacket OnHandlePacket(byte cmd, DataArchive reader)
    {
        IPacket packet = cmd switch
        {
            MessageTypes.LoginResponse => new LoginPacket(),
            MessageTypes.HeartbeatResponse => new HeartbeatPacket(),
            MessageTypes.LogoutResponse => new LogoutPacket(),
            MessageTypes.LuckyDrawResponse => new LuckyDrawPacket(),
            MessageTypes.LuckyStatusResponse => new LuckyStatusPacket(),
            MessageTypes.CDKeyActivateResponse => new CDKeyActivatePacket(),
            MessageTypes.UpdateConfigResponse => new UpdateConfigPacket(),
            MessageTypes.Error => new LoginPacket(), //Reuse LoginPacket for error responses

            _ => throw new NotSupportedException($"Unsupported message type: 0x{cmd:X2}"),
        };

        packet.Read(reader);

        // 保存登录成功后的token
        if (packet is LoginPacket login && login.ErrorCode == 0)
        {
            SessionToken = login.Token;
            Debug.WriteLine($"[INFO] 登录成功，Token已保存: {SessionToken}");

            // 开始心跳
            StartHeartbeat();
        }

        return packet;
    }

    private void StartHeartbeat()
    {
        if (_heartbeatRunning) return;

        _heartbeatRunning = true;
        _heartbeatThread = new Thread(Heartbeat) { IsBackground = true };
        _heartbeatThread.Start();
    }

    private void StopHeartbeat()
    {
        _heartbeatRunning = false;

        // 等待心跳线程结束，减少等待时间避免程序关闭时卡顿
        if (_heartbeatThread != null)
        {
            try
            {
                if (!_heartbeatThread.Join(500)) // 减少到500毫秒，避免关闭时卡顿
                {
                    Debug.WriteLine("[WARNING] 心跳线程未能在超时时间内结束，继续关闭");
                    // 注意：Thread.Abort在.NET Core中已被移除，这里只是记录警告
                }
                _heartbeatThread = null;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] 停止心跳线程时发生异常: {ex.Message}");
            }
        }
    }

    private async void Heartbeat()
    {
        int consecutiveFailures = 0;
        const int maxConsecutiveFailures = 3;

        try
        {
            while (_heartbeatRunning && IsConnected)
            {
                try
                {
                    if (!IsConnected || !_heartbeatRunning)
                    {
                        Debug.WriteLine("连接已断开或心跳已停止，退出心跳循环");
                        break;
                    }

                    Debug.WriteLine("发送心跳...");
                    var success = await SendHeartbeatAsync();
                    if (success)
                    {
                        consecutiveFailures = 0; // 重置失败计数

                        // 使用CancellationToken来支持快速退出
                        using var cts = new CancellationTokenSource();
                        try
                        {
                            await Task.Delay(HeartbeatInterval, cts.Token);
                        }
                        catch (OperationCanceledException)
                        {
                            // 心跳被取消，正常退出
                            break;
                        }
                    }
                    else
                    {
                        consecutiveFailures++;
                        Debug.WriteLine($"心跳失败 (连续失败: {consecutiveFailures}/{maxConsecutiveFailures})");

                        if (consecutiveFailures >= maxConsecutiveFailures)
                        {
                            Debug.WriteLine("连续心跳失败次数过多，触发登出");
                            OnHeartbeatFailed();
                            break;
                        }

                        // 失败后等待较短时间再重试
                        using var cts = new CancellationTokenSource();
                        try
                        {
                            await Task.Delay(5000, cts.Token);
                        }
                        catch (OperationCanceledException)
                        {
                            // 心跳被取消，正常退出
                            break;
                        }
                    }
                }
                catch (Exception ex)
                {
                    consecutiveFailures++;
                    Debug.WriteLine($"心跳异常 (连续失败: {consecutiveFailures}/{maxConsecutiveFailures}): {ex.Message}");

                    if (consecutiveFailures >= maxConsecutiveFailures)
                    {
                        Debug.WriteLine("连续心跳异常次数过多，触发登出");
                        OnHeartbeatFailed();
                        break;
                    }

                    // 异常后等待较短时间再重试
                    using var cts = new CancellationTokenSource();
                    try
                    {
                        await Task.Delay(5000, cts.Token);
                    }
                    catch (OperationCanceledException)
                    {
                        // 心跳被取消，正常退出
                        break;
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] 心跳线程发生未处理异常: {ex.Message}");
        }
        finally
        {
            Debug.WriteLine("心跳线程已退出");
            _heartbeatRunning = false;
        }
    }

    private async Task<bool> SendHeartbeatAsync()
    {
        try
        {
            if (!IsConnected)
            {
                Debug.WriteLine("连接已断开，心跳失败");
                return false;
            }

            SendPacket(new HeartbeatPacket { Token = SessionToken }, MessageTypes.Heartbeat);

            // 减少超时时间和重试次数，避免服务端掉线时长时间阻塞
            var response = await WaitForResponseWithRetry(MessageTypes.HeartbeatResponse, 2, 10 * 1000);
            if (response is HeartbeatPacket hbResponse)
            {
                if (hbResponse.ErrorCode != 0)
                {
                    Debug.WriteLine($"心跳失败 - Error Code: {hbResponse.ErrorCode}, Message: {hbResponse.ErrorMessage}");
                    return false;
                }

                Debug.WriteLine($"心跳成功 - 当前在线用户数: {hbResponse.OnlineUserCount}, 强制更新: {hbResponse.ForceUpdate}");

                // 检查是否需要强制更新
                if (hbResponse.ForceUpdate)
                {
                    Debug.WriteLine("[FORCE UPDATE] 服务器要求强制更新，准备退出应用程序");

                    // 在UI线程中显示提示并关闭程序
                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        System.Windows.MessageBox.Show(
                            "服务器已发布新版本，应用程序将自动退出进行更新。\n请重新启动应用程序以获取最新版本。",
                            "强制更新通知",
                            System.Windows.MessageBoxButton.OK,
                            System.Windows.MessageBoxImage.Information);

                        // 强制退出应用程序
                        Environment.Exit(0);
                    });

                    return false; // 返回false以停止心跳
                }

                return true;
            }

            Debug.WriteLine("心跳响应类型异常");
            return false;
        }
        catch (TimeoutException)
        {
            Debug.WriteLine("心跳超时");
            return false;
        }
        catch (InvalidOperationException ex) when (ex.Message.Contains("连接已断开"))
        {
            Debug.WriteLine("连接已断开，心跳失败");
            return false;
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"发送心跳异常: {ex.Message}");
            return false;
        }
    }

    private void OnHeartbeatFailed()
    {
        _heartbeatRunning = false;
        HeartbeatFailed?.Invoke(this, EventArgs.Empty);
    }

    /// <summary>
    /// 发送注销请求到服务器
    /// </summary>
    public async Task<bool> LogoutAsync()
    {
        if (string.IsNullOrEmpty(SessionToken))
        {
            Debug.WriteLine("[INFO] 没有有效的会话Token，跳过注销");
            return true;
        }

        try
        {
            Debug.WriteLine("[INFO] 发送注销请求...");
            SendPacket(new LogoutPacket { Token = SessionToken }, MessageTypes.Logout);

            // 等待注销响应，设置更短的超时时间，避免程序关闭时卡顿
            var response = await WaitForResponseWithRetry(MessageTypes.LogoutResponse, 1, 2 * 1000);
            if (response is LogoutPacket logoutResponse)
            {
                if (logoutResponse.ErrorCode != 0)
                {
                    Debug.WriteLine($"注销失败 - Error Code: {logoutResponse.ErrorCode}, Message: {logoutResponse.ErrorMessage}");
                    return false;
                }

                Debug.WriteLine("[INFO] 注销成功");
                return true;
            }

            Debug.WriteLine("[WARNING] 注销响应类型异常");
            return false;
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] 注销异常: {ex.Message}");
            return false;
        }
        finally
        {
            // 无论注销是否成功，都清理本地状态
            SessionToken = null;
        }
    }

    /// <summary>
    /// 发送签到抽奖请求
    /// </summary>
    public async Task<LuckyDrawPacket?> LuckyDrawAsync()
    {
        if (string.IsNullOrEmpty(SessionToken))
        {
            Debug.WriteLine("[ERROR] 没有有效的会话Token，无法进行签到");
            return null;
        }

        try
        {
            Debug.WriteLine("[INFO] 发送签到抽奖请求...");
            SendPacket(new LuckyDrawPacket { Token = SessionToken }, MessageTypes.LuckyDraw);

            // 等待签到抽奖响应
            var response = await WaitForResponseWithRetry(MessageTypes.LuckyDrawResponse, 3, 30 * 1000);

            if (response is LuckyDrawPacket drawResponse)
            {
                if (drawResponse.ErrorCode != 0)
                {
                    Debug.WriteLine($"签到失败 - Error Code: {drawResponse.ErrorCode}, Message: {drawResponse.ErrorMessage}");
                    return drawResponse;
                }

                Debug.WriteLine($"签到成功 - 奖励: {drawResponse.RewardMessage}, 数量: {drawResponse.RewardCount}");
                return drawResponse;
            }

            Debug.WriteLine("[WARNING] 签到响应类型异常");
            return null;
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] 签到异常: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// 获取签到状态
    /// </summary>
    public async Task<LuckyStatusPacket?> GetLuckyStatusAsync()
    {
        if (string.IsNullOrEmpty(SessionToken))
        {
            Debug.WriteLine("[ERROR] 没有有效的会话Token，无法获取签到状态");
            return null;
        }

        try
        {
            Debug.WriteLine("[INFO] 获取签到状态...");
            SendPacket(new LuckyStatusPacket { Token = SessionToken }, MessageTypes.LuckyStatus);

            // 等待签到状态响应
            var response = await WaitForResponseWithRetry(MessageTypes.LuckyStatusResponse, 3, 30 * 1000);
            if (response is LuckyStatusPacket statusResponse)
            {
                if (statusResponse.ErrorCode != 0) return statusResponse;

                Debug.WriteLine($"获取签到状态成功 - 连续天数: {statusResponse.Point}, 可用次数: {statusResponse.AvailableCount}");
                return statusResponse;
            }

            Debug.WriteLine("[WARNING] 签到状态响应类型异常");
            return null;
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] 获取签到状态异常: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// 发送CDKEY激活请求
    /// </summary>
    /// <param name="cdkey">要激活的CDKEY</param>
    /// <param name="isVerified">是否已验证群成员身份</param>
    /// <returns>激活响应</returns>
    public async Task<CDKeyActivatePacket?> ActivateCDKeyAsync(string cdkey, bool isVerified = false)
    {
        if (string.IsNullOrEmpty(SessionToken))
        {
            Debug.WriteLine("[ERROR] 没有有效的会话Token，无法激活CDKEY");
            return null;
        }

        if (string.IsNullOrWhiteSpace(cdkey))
        {
            Debug.WriteLine("[ERROR] CDKEY不能为空");
            return null;
        }

        // 检查本地CDKey请求时间缓存（仅在第一次提交时检查）
        if (!isVerified)
        {
            var timeSinceLastRequest = DateTime.Now - lastRequestTime;
            if (timeSinceLastRequest.TotalSeconds < 30)
            {
                var remainingSeconds = 30 - (int)timeSinceLastRequest.TotalSeconds;
                Debug.WriteLine($"[WARN] CDKey请求过于频繁，请等待 {remainingSeconds} 秒后再试");

                // 返回一个错误响应，模拟服务端的频率限制响应
                return new CDKeyActivatePacket
                {
                    ErrorCode = 429, // Too Many Requests
                    ErrorMessage = $"请求过于频繁，请等待 {remainingSeconds} 秒后再试"
                };
            }

            lastRequestTime = DateTime.Now;
        }

        try
        {
            Debug.WriteLine($"[INFO] 发送CDKEY激活请求: {cdkey}, IsVerified: {isVerified}");
            SendPacket(new CDKeyActivatePacket
            {
                Token = SessionToken,
                CDKey = cdkey.Trim(),
                IsVerified = isVerified
            }, MessageTypes.CDKeyActivate);

            // 等待CDKEY激活响应
            var response = await WaitForResponseWithRetry(MessageTypes.CDKeyActivateResponse, 3, 5 * 1000);
            if (response is CDKeyActivatePacket cdkeyResponse) return cdkeyResponse;
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] CDKEY激活请求异常: {ex.Message}");
        }

        return null;
    }

    /// <summary>
    /// CDKey请求时间缓存，用于减少服务端通信压力
    /// </summary>
    private static DateTime lastRequestTime = new();

    public override void Dispose()
    {
        try
        {
            Debug.WriteLine("[INFO] 开始释放BnszsSession资源");

            // 停止心跳
            StopHeartbeat();

            // 清理事件订阅
            HeartbeatFailed = null;

            // 清理会话信息
            SessionToken = null;

            // 调用基类的Dispose
            base.Dispose();

            Debug.WriteLine("[INFO] BnszsSession资源释放完成");
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] 释放BnszsSession资源时发生异常: {ex.Message}");
        }
    }
    #endregion
}
