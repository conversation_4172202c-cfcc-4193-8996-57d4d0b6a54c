{layout name="manage/template" /} 
<div class="admin-content">
	<div class="admin-content-body">
		<div class="am-cf am-padding am-padding-bottom-0">
			<div class="am-fl am-cf">
				<strong class="am-text-primary am-text-lg">风控仪表板</strong> / <small>Risk Control Dashboard</small>
			</div>
		</div>
		<hr>
		<!-- 统计卡片 -->
		<div class="am-g">
			<div class="am-u-sm-12 am-u-md-3">
				<div class="stats-card">
					<div class="stats-number">{$stats.total_events}</div>
					<div class="stats-label">今日风险事件</div>
				</div>
			</div>
			<div class="am-u-sm-12 am-u-md-3">
				<div class="stats-card">
					<div class="stats-number status-pending">{$stats.pending_events}</div>
					<div class="stats-label">待处理事件</div>
				</div>
			</div>
			<div class="am-u-sm-12 am-u-md-3">
				<div class="stats-card">
					<div class="stats-number severity-high">{$stats.high_severity}</div>
					<div class="stats-label">高风险事件</div>
				</div>
			</div>
			<div class="am-u-sm-12 am-u-md-3">
				<div class="stats-card">
					<div class="stats-number">{$stats.audit_users}</div>
					<div class="stats-label">审核状态用户</div>
				</div>
			</div>
		</div>
		<!-- 快速操作 -->
		<div class="am-panel am-panel-default">
			<div class="am-panel-hd">快速操作</div>
			<div class="am-panel-bd">
				<div class="am-btn-toolbar">
					<a href="/manage/admin/risk/events" class="am-btn am-btn-primary">
						<i class="am-icon-list"></i> 查看风险事件 </a>
					<a href="/manage/admin/risk/events?status=pending" class="am-btn am-btn-warning">
						<i class="am-icon-exclamation-triangle"></i> 待处理事件 </a>
					<a href="/manage/admin/signin" class="am-btn am-btn-success">
						<i class="am-icon-calendar-check-o"></i> 签到管理 </a>
					<a href="/manage/admin/risk/config" class="am-btn am-btn-secondary">
						<i class="am-icon-cog"></i> 风控配置 </a>
				</div>
			</div>
		</div>
		<!-- 最近的风险事件 -->
		<div class="am-panel am-panel-default">
			<div class="am-panel-hd">最近的风险事件</div>
			<div class="am-panel-bd"> {if condition="$recent_events"} {volist name="recent_events" id="event"} <div class="event-item {$event.severity}">
					<div class="am-cf">
						<div class="am-fl">
							<strong> {switch name="event.event_type"} {case value="device_qq_limit"}设备QQ数量超限{/case} {case value="ip_qq_limit"}IP QQ数量超限{/case} {case value="frequent_login"}频繁登录{/case} {default /}未知事件类型 {/switch} </strong>
							<span class="am-badge am-badge-{if condition='$event.severity == " high"'}danger{elseif condition='$event.severity == "medium"' }warning{else/}success{/if} am-margin-left-sm"> {$event.severity} </span>
						</div>
						<div class="am-fr">
							<span class="am-badge 
                                            {if condition='$event.status == " pending"'}am-badge-warning {elseif condition='$event.status == "processed"' }am-badge-success {else/}am-badge-secondary{/if}"> {$event.status} </span>
						</div>
					</div>
					<div class="am-margin-top-xs">
						<small class="am-text-muted"> {$event.description} | {if condition="$event.device_id"}设备: {$event.device_id} | {/if} {if condition="$event.ip_address"}IP: {$event.ip_address} | {/if} 时间: {$event.created_at} </small>
					</div>
				</div> {/volist} {else/} <div class="am-text-center am-padding">
					<i class="am-icon-info-circle am-text-muted"></i>
					<span class="am-text-muted">暂无风险事件</span>
				</div> {/if} </div>
		</div>
		<!-- 系统状态 -->
		<div class="am-panel am-panel-default">
			<div class="am-panel-hd">系统状态</div>
			<div class="am-panel-bd">
				<div class="am-g">
					<div class="am-u-sm-6">
						<p><strong>风控服务状态:</strong> <span class="am-text-success">正常运行</span></p>
						<p><strong>数据库连接:</strong> <span class="am-text-success">正常</span></p>
					</div>
					<div class="am-u-sm-6">
						<p><strong>当前管理员:</strong> {$admin_info.username}</p>
						<p><strong>权限级别:</strong> {if condition="$admin_info.super == 1"} <span class="am-text-danger">超级管理员</span> {else/} <span class="am-text-primary">普通管理员</span> {/if} </p>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<script>
	// 自动刷新数据
	setInterval(function() {
	    location.reload();
	}, 60000); // 每分钟刷新一次
</script>
<style>
	.stats-card {
	    background: #fff;
	    border-radius: 4px;
	    padding: 20px;
	    margin-bottom: 20px;
	    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
	}
	.stats-number {
	    font-size: 2.5em;
	    font-weight: bold;
	    color: #3c8dbc;
	}
	.stats-label {
	    color: #666;
	    font-size: 14px;
	    margin-top: 5px;
	}
	.severity-high { color: #d73925; }
	.severity-medium { color: #f39c12; }
	.severity-low { color: #00a65a; }
	.status-pending { color: #f39c12; }
	.status-processed { color: #00a65a; }
	.status-ignored { color: #999; }
	.event-item {
	    border-left: 4px solid #ddd;
	    padding: 10px 15px;
	    margin-bottom: 10px;
	    background: #f9f9f9;
	}
	.event-item.high { border-left-color: #d73925; }
	.event-item.medium { border-left-color: #f39c12; }
	.event-item.low { border-left-color: #00a65a; }
</style>