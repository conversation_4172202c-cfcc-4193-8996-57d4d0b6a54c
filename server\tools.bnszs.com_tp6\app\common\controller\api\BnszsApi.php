<?php

namespace app\common\controller\api;

use app\common\controller\ApiBase;
use think\App;
use think\Request;
use think\facade\Cache;
use think\facade\Session;
use app\common\service\RedisService;
use app\common\model\Team as Team;
use app\guidebook\model\Clock as Clock;

// 此类下的接口请求会进行鉴权
class BnszsApi extends ApiBase
{
    public function __construct(App $app, Request $request) {
        parent::__construct($app, $request);
    }

    /**
     * 获取团队信息
     */
    public function team() {
        $teams = Team::GetTeams();
        $ret = [];
            
        foreach ($teams as $key => $value) {
            if (isset($value['uin'])) {
                $value['head'] = "http://q1.qlogo.cn/g?b=qq&nk=".$value['uin']."&s=100";
                unset($value['uin']);
            }
                
            if ($value['type'] == 'author') {
                $ret['author'][] = $value;
            } else {
                $ret['friends'][] = $value;
            }
        }

        return json($ret);
    }

    /**
     * 获取时间表信息
     */
    public function schedule() {
        $type = $this->request->param('server', 'ZTX');
        $ret = Clock::getClockData($type);
        return json($ret);
    }
}
