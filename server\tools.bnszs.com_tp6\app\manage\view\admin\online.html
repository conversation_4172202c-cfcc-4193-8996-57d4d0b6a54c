{layout name="manage/template" /}

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<div class="admin-content">
    <div class="admin-content-body">
        <div class="am-cf am-padding am-padding-bottom-0">
            <div class="am-fl am-cf">
                <strong class="am-text-primary am-text-lg">在线用户统计</strong> / <small>Online Users Statistics</small>
            </div>
        </div>
        <hr>

        <!-- 统计卡片 -->
        <div class="am-g">
            <div class="am-u-sm-12 am-u-md-3">
                <div class="stats-card">
                    <div class="stats-number" id="current-online">{$online_stats.online_count|default=0}</div>
                    <div class="stats-label">当前在线</div>
                </div>
            </div>
            <div class="am-u-sm-12 am-u-md-3">
                <div class="stats-card">
                    <div class="stats-number" id="auth-users">{$online_stats.auth_stats.active_users|default=0}</div>
                    <div class="stats-label">认证用户</div>
                </div>
            </div>
            <div class="am-u-sm-12 am-u-md-3">
                <div class="stats-card">
                    <div class="stats-number" id="active-devices">{$online_stats.heartbeat_stats.active_devices|default=0}</div>
                    <div class="stats-label">活跃设备</div>
                </div>
            </div>
            <div class="am-u-sm-12 am-u-md-3">
                <div class="stats-card">
                    {if condition="$online_stats.status == 'ok'"}
                    <div class="stats-number status-normal" id="system-status">正常</div>
                    {else /}
                    <div class="stats-number status-error" id="system-status">异常</div>
                    {/if}
                    <div class="stats-label">系统状态</div>
                </div>
            </div>
        </div>

        <!-- 图表区域 -->
        <div class="am-g am-margin-top">
            <div class="am-u-sm-12 am-u-md-8">
                <div class="am-panel am-panel-default">
                    <div class="am-panel-hd">
                        在线用户趋势
                        <div class="am-fr">
                            <button type="button" class="am-btn am-btn-xs am-btn-primary" onclick="changePeriod('hour')">小时</button>
                            <button type="button" class="am-btn am-btn-xs am-btn-default" onclick="changePeriod('day')">天</button>
                            <button type="button" class="am-btn am-btn-xs am-btn-secondary" onclick="toggleAutoRefresh()">
                                <span id="auto-refresh-text">开启自动刷新</span>
                            </button>
                            <button type="button" class="am-btn am-btn-xs am-btn-warning" onclick="debugChart()">
                                调试
                            </button>
                        </div>
                    </div>
                    <div class="am-panel-bd">
                        <canvas id="onlineChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>

            <!-- 在线用户列表 -->
            <div class="am-u-sm-12 am-u-md-4">
                <div class="am-panel am-panel-default">
                    <div class="am-panel-hd">
                        在线用户列表
                        <button type="button" class="am-btn am-btn-xs am-btn-default am-fr" onclick="refreshUserList()">
                            <i class="am-icon-refresh"></i> 刷新
                        </button>
                    </div>
                    <div class="am-panel-bd" style="height: 400px; overflow-y: auto;">
                        <div id="online-users-list">
                            <div class="am-text-center" style="padding: 50px 0;">
                                <i class="am-icon-spinner am-icon-spin"></i>
                                <p>加载中...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 详细信息区域 -->
        <div class="am-g am-margin-top">
            <div class="am-u-sm-12">
                <div class="am-panel am-panel-default">
                    <div class="am-panel-hd">系统详细信息</div>
                    <div class="am-panel-bd">
                        <div class="am-g">
                            <div class="am-u-sm-12 am-u-md-6">
                                <h4>认证服务统计</h4>
                                <table class="am-table am-table-striped am-table-compact">
                                    <tbody>
                                        <tr><td>总Token数</td><td id="total-tokens">{$online_stats.auth_stats.total_tokens|default=0}</td></tr>
                                        <tr><td>活跃用户数</td><td id="active-users-detail">{$online_stats.auth_stats.active_users|default=0}</td></tr>
                                        <tr><td>最后更新</td><td id="auth-timestamp">未知</td></tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="am-u-sm-12 am-u-md-6">
                                <h4>心跳服务统计</h4>
                                <table class="am-table am-table-striped am-table-compact">
                                    <tbody>
                                        <tr><td>总设备数</td><td id="total-devices">{$online_stats.heartbeat_stats.total_devices|default=0}</td></tr>
                                        <tr><td>活跃设备数</td><td id="active-devices-detail">{$online_stats.heartbeat_stats.active_devices|default=0}</td></tr>
                                        <tr><td>非活跃设备数</td><td id="inactive-devices">{$online_stats.heartbeat_stats.inactive_devices|default=0}</td></tr>
                                        <tr><td>最后更新</td><td id="heartbeat-timestamp">未知</td></tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

    <script>
    $(document).ready(function(){

        // 全局变量
        var chart = null;
        var currentPeriod = 'hour';
        var autoRefreshInterval = null;
        var isAutoRefresh = false;

        // 页面加载完成后初始化
        $(document).ready(function() {
            // 检查必要的依赖
            if (typeof $ === 'undefined') {
                console.error('jQuery未加载');
                alert('页面加载失败: jQuery未加载');
                return;
            }

            if (typeof layer === 'undefined') {
                console.error('Layer未加载');
                alert('页面加载失败: Layer未加载');
                return;
            }

            // 等待Chart.js加载完成
            var checkChart = function() {
                if (typeof Chart !== 'undefined') {
                    // 初始化图表
                    initChart();

                    // 加载在线用户列表
                    loadOnlineUsers();

                    // 每30秒自动刷新统计数据
                    setInterval(function() {
                        refreshStats();
                    }, 30000);
                } else {
                    console.log('等待Chart.js加载...');
                    setTimeout(checkChart, 100);
                }
            };

            checkChart();
        });

        // 初始化图表
        function initChart() {
            try {
                var chartElement = document.getElementById('onlineChart');
                if (!chartElement) {
                    console.error('找不到图表元素 #onlineChart');
                    layer.msg('图表初始化失败: 找不到图表容器', {icon: 2});
                    return;
                }

                var ctx = chartElement.getContext('2d');
                if (!ctx) {
                    console.error('无法获取图表上下文');
                    layer.msg('图表初始化失败: 无法获取上下文', {icon: 2});
                    return;
                }

                // 检查Chart.js是否加载
                if (typeof Chart === 'undefined') {
                    console.error('Chart.js未加载');
                    layer.msg('图表初始化失败: Chart.js未加载', {icon: 2});
                    return;
                }

                chart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: [],
                        datasets: [{
                            label: '在线用户数',
                            data: [],
                            borderColor: 'rgb(75, 192, 192)',
                            backgroundColor: 'rgba(75, 192, 192, 0.2)',
                            tension: 0.1
                        }]
                    },
                    options: {
                        responsive: true,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });

                // 加载图表数据
                loadChartData();
            } catch (e) {
                console.error('图表初始化失败:', e);
                layer.msg('图表初始化失败: ' + e.message, {icon: 2});
            }
        }

        // 加载图表数据
        function loadChartData() {
            $.ajax({
                url: '/manage/admin/online',
                type: 'POST',
                data: {
                    action: 'chart',
                    period: currentPeriod,
                    limit: currentPeriod === 'hour' ? 24 : 30
                },
                dataType: 'json',
                success: function(response) {
                    try {
                        if (response && response.code === 1 && response.data) {
                            if (chart && chart.data) {
                                chart.data.labels = response.data.labels || [];
                                if (response.data.datasets && response.data.datasets[0]) {
                                    chart.data.datasets[0].data = response.data.datasets[0].data || [];
                                }
                                chart.update();
                            }
                        } else {
                            var errorMsg = response && response.msg ? response.msg : '未知错误';
                            layer.msg('加载图表数据失败: ' + errorMsg, {icon: 2});
                        }
                    } catch (e) {
                        console.error('处理图表数据时出错:', e);
                        layer.msg('处理图表数据时出错: ' + e.message, {icon: 2});
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX请求失败:', {
                        xhr: xhr,
                        status: status,
                        error: error,
                        readyState: xhr.readyState,
                        responseText: xhr.responseText,
                        statusCode: xhr.status,
                        statusText: xhr.statusText,
                        headers: xhr.getAllResponseHeaders()
                    });

                    var errorMsg = '网络错误';
                    var debugInfo = '';

                    // 详细错误分析
                    if (xhr.status === 0) {
                        errorMsg = '网络连接失败或被阻止';
                        debugInfo = '可能是CORS问题或网络不通';
                    } else if (xhr.status === 404) {
                        errorMsg = '接口不存在 (404)';
                        debugInfo = '路由配置可能有问题';
                    } else if (xhr.status === 500) {
                        errorMsg = '服务器内部错误 (500)';
                        debugInfo = '检查PHP错误日志';
                    } else if (xhr.status === 403) {
                        errorMsg = '权限不足 (403)';
                        debugInfo = '可能需要重新登录';
                    } else if (xhr.responseText) {
                        try {
                            var response = JSON.parse(xhr.responseText);
                            errorMsg = response.msg || errorMsg;
                            debugInfo = response.debug ? JSON.stringify(response.debug) : '';
                        } catch (e) {
                            errorMsg = xhr.responseText.substring(0, 100);
                            debugInfo = '响应不是有效的JSON格式';
                        }
                    }

                    console.log('错误详情:', debugInfo);
                    layer.msg('加载图表数据失败: ' + errorMsg, {icon: 2});

                    // 如果是权限问题，提示重新登录
                    if (xhr.status === 403 || errorMsg.includes('登录')) {
                        setTimeout(function() {
                            if (confirm('登录状态可能已过期，是否重新登录？')) {
                                window.location.href = '/manage/login?type=admin';
                            }
                        }, 2000);
                    }
                }
            });
        }

        // 切换时间周期
        window.changePeriod = function(period) {
            currentPeriod = period;
            loadChartData();

            // 更新按钮状态
            $('.am-panel-hd .am-btn').removeClass('am-btn-primary').addClass('am-btn-default');
            $('[onclick="changePeriod(\'' + period + '\')"]').removeClass('am-btn-default').addClass('am-btn-primary');
        };

        // 切换自动刷新
        window.toggleAutoRefresh = function() {
            if (isAutoRefresh) {
                clearInterval(autoRefreshInterval);
                isAutoRefresh = false;
                $('#auto-refresh-text').text('开启自动刷新');
            } else {
                autoRefreshInterval = setInterval(function() {
                    loadChartData();
                    refreshStats();
                }, 10000); // 10秒刷新一次
                isAutoRefresh = true;
                $('#auto-refresh-text').text('关闭自动刷新');
            }
        };

        // 调试图表功能
        window.debugChart = function() {
            console.log('=== 图表调试信息 ===');
            console.log('Chart对象:', chart);
            console.log('当前周期:', currentPeriod);
            console.log('自动刷新状态:', isAutoRefresh);

            // 测试直接调试接口
            console.log('测试直接调试接口...');
            $.ajax({
                url: '/debug_chart_direct.php',
                type: 'GET',
                data: {
                    period: currentPeriod,
                    limit: currentPeriod === 'hour' ? 24 : 30
                },
                dataType: 'json',
                success: function(response) {
                    console.log('直接调试响应:', response);
                    if (response.debug) {
                        console.log('调试详情:', response.debug);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('直接调试错误:', {xhr: xhr, status: status, error: error});
                    console.log('响应文本:', xhr.responseText);
                }
            });

            // 测试API接口
            console.log('测试chart接口...');
            $.ajax({
                url: '/manage/admin/online',
                type: 'POST',
                data: {
                    action: 'chart',
                    period: currentPeriod,
                    limit: currentPeriod === 'hour' ? 24 : 30
                },
                dataType: 'json',
                success: function(response) {
                    console.log('Chart API响应:', response);
                    layer.msg('调试信息已输出到控制台，请按F12查看', {icon: 1});
                },
                error: function(xhr, status, error) {
                    console.error('Chart API错误:', {xhr: xhr, status: status, error: error});
                    console.log('响应状态:', xhr.status);
                    console.log('响应文本:', xhr.responseText);
                    layer.msg('API请求失败，详情请查看控制台', {icon: 2});
                }
            });

            // 测试realtime接口
            console.log('测试realtime接口...');
            $.ajax({
                url: '/manage/admin/online',
                type: 'POST',
                data: {
                    action: 'realtime'
                },
                dataType: 'json',
                success: function(response) {
                    console.log('Realtime API响应:', response);
                },
                error: function(xhr, status, error) {
                    console.error('Realtime API错误:', {xhr: xhr, status: status, error: error});
                    console.log('响应文本:', xhr.responseText);
                }
            });
        };

        // 刷新统计数据
        function refreshStats() {
            $.ajax({
                url: '/manage/admin/online',
                type: 'POST',
                data: {
                    action: 'realtime'
                },
                dataType: 'json',
                success: function(response) {
                    try {
                        if (response && response.code === 1 && response.data) {
                            var data = response.data;

                            // 更新统计卡片
                            $('#current-online').text(data.online_count || 0);
                            if (data.auth_stats) {
                                $('#auth-users').text(data.auth_stats.active_users || 0);
                                $('#total-tokens').text(data.auth_stats.total_tokens || 0);
                                $('#active-users-detail').text(data.auth_stats.active_users || 0);

                                // 更新时间戳
                                if (data.auth_stats.timestamp) {
                                    $('#auth-timestamp').text(new Date(data.auth_stats.timestamp * 1000).toLocaleString());
                                }
                            }

                            if (data.heartbeat_stats) {
                                $('#active-devices').text(data.heartbeat_stats.active_devices || 0);
                                $('#total-devices').text(data.heartbeat_stats.total_devices || 0);
                                $('#active-devices-detail').text(data.heartbeat_stats.active_devices || 0);
                                $('#inactive-devices').text(data.heartbeat_stats.inactive_devices || 0);

                                // 更新时间戳
                                if (data.heartbeat_stats.timestamp) {
                                    $('#heartbeat-timestamp').text(new Date(data.heartbeat_stats.timestamp * 1000).toLocaleString());
                                }
                            }

                            // 更新系统状态
                            var statusText = data.status === 'ok' ? '正常' : '异常';
                            var statusColor = data.status === 'ok' ? '#5FB878' : '#FF5722';
                            $('#system-status').text(statusText).css('color', statusColor);
                        } else {
                            console.warn('刷新统计数据失败:', response);
                        }
                    } catch (e) {
                        console.error('处理统计数据时出错:', e);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('刷新统计数据请求失败:', {xhr: xhr, status: status, error: error});
                }
            });
        }

        // 加载在线用户列表
        function loadOnlineUsers() {
            $.ajax({
                url: '/manage/admin/online',
                type: 'POST',
                data: {
                    action: 'users'
                },
                dataType: 'json',
                success: function(response) {
                    if (response.code === 1) {
                        var users = response.data.users || [];
                        var html = '';
                        
                        if (users.length === 0) {
                            html = '<div class="am-text-center" style="padding: 50px 0;"><p>暂无在线用户</p></div>';
                        } else {
                            users.forEach(function(user) {
                                var loginTime = new Date(user.login_time * 1000).toLocaleString();
                                html += '<div class="am-panel am-panel-default am-margin-bottom-sm">';
                                html += '<div class="am-panel-bd" style="padding: 10px;">';
                                html += '<div><strong>QQ: ' + user.uin + '</strong></div>';
                                html += '<div class="am-text-sm am-text-secondary">登录时间: ' + loginTime + '</div>';
                                html += '</div></div>';
                            });
                        }

                        $('#online-users-list').html(html);
                    } else {
                        $('#online-users-list').html('<div class="am-text-center" style="padding: 50px 0;"><p>加载失败</p></div>');
                    }
                },
                error: function() {
                    $('#online-users-list').html('<div class="am-text-center" style="padding: 50px 0;"><p>网络错误</p></div>');
                }
            });
        }

        // 刷新用户列表
        window.refreshUserList = function() {
            loadOnlineUsers();
        };
    });
    </script>

    <style>
    .stats-card {
        background: #fff;
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 20px;
        text-align: center;
        margin-bottom: 15px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .stats-number {
        font-size: 2.5em;
        font-weight: bold;
        margin-bottom: 5px;
        color: #0e90d2;
    }

    .stats-number.status-normal {
        color: #5cb85c;
    }

    .stats-number.status-error {
        color: #d9534f;
    }

    .stats-label {
        color: #999;
        font-size: 0.9em;
        margin: 0;
    }

    .am-panel-bd canvas {
        max-width: 100%;
        height: auto;
    }
    </style>
