package main

import (
	"fmt"
	"net"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"time"
	"udp-server/server/internal/database"
	"udp-server/server/internal/handler"
	"udp-server/server/internal/model"
	"udp-server/server/internal/service"
	"udp-server/server/pkg/binary"
	"udp-server/server/pkg/cache"
	"udp-server/server/pkg/logger"
	"udp-server/server/pkg/metrics"

	"github.com/gorilla/mux"
	"github.com/spf13/viper"
	"gorm.io/gorm"
)

func main() {
	// 初始化配置
	initConfig()

	// 初始化数据库
	if err := database.InitDB(); err != nil {
		// 在logger初始化之前，使用fmt.Printf输出错误
		fmt.Printf("[Fatal] Failed to initialize database: %v\n", err)
		os.Exit(1)
	}

	// 创建 metrics 实例
	metricsInstance := metrics.NewMetrics()

	// 配置已在initConfig()中初始化

	// 初始化日志系统
	logConfig := &logger.Config{
		Level:    viper.GetString("log.level"),
		Console:  viper.GetBool("log.console"),
		File:     viper.GetBool("log.file"),
		FilePath: viper.GetString("log.file_path"),
	}
	if err := logger.Init(logConfig); err != nil {
		// 在logger初始化失败时，使用fmt.Printf输出错误
		fmt.Printf("[Fatal] Failed to initialize logger: %v\n", err)
		os.Exit(1)
	}
	defer logger.Close()

	logger.Info("服务器启动中...")

	// 创建Redis缓存实例（强制使用Redis）
	redisHost := viper.GetString("redis.host")
	redisPort := viper.GetInt("redis.port")
	redisPassword := viper.GetString("redis.password")
	redisDB := viper.GetInt("redis.db")

	logger.Info("正在连接Redis缓存服务器 %s:%d...", redisHost, redisPort)

	// 强制使用Redis缓存，连接失败则退出
	cacheInstance, err := cache.NewRedisCache(redisHost, redisPort, redisPassword, redisDB)
	if err != nil {
		logger.Error("Redis缓存服务器连接失败: %v", err)
		logger.Error("请检查以下配置:")
		logger.Error("  - Redis服务器地址: %s:%d", redisHost, redisPort)
		logger.Error("  - Redis数据库: %d", redisDB)
		logger.Error("  - Redis服务是否正常运行")
		logger.Error("  - 网络连接是否正常")
		if redisPassword != "" {
			logger.Error("  - Redis密码是否正确")
		}
		logger.Error("服务器启动失败")
		os.Exit(1)
	}

	// 执行Redis功能测试
	logger.Info("正在测试Redis缓存功能...")
	testKey := fmt.Sprintf("server_startup_test_%d", time.Now().Unix())
	testValue := "startup_test_value"

	// 测试写入
	if err := cacheInstance.Set(testKey, testValue, 30*time.Second); err != nil {
		logger.Error("Redis缓存写入测试失败: %v", err)
		logger.Error("服务器启动失败，请检查Redis服务权限配置")
		os.Exit(1)
	}

	// 测试读取
	var readValue string
	if err := cacheInstance.Get(testKey, &readValue); err != nil {
		logger.Error("Redis缓存读取测试失败: %v", err)
		logger.Error("服务器启动失败，请检查Redis服务配置")
		os.Exit(1)
	}

	// 验证数据一致性
	if readValue != testValue {
		logger.Error("Redis缓存数据一致性测试失败: 期望 '%s', 实际 '%s'", testValue, readValue)
		logger.Error("服务器启动失败，请检查Redis服务配置")
		os.Exit(1)
	}

	// 测试删除
	if err := cacheInstance.Delete(testKey); err != nil {
		logger.Error("Redis缓存删除测试失败: %v", err)
		logger.Error("服务器启动失败，请检查Redis服务权限配置")
		os.Exit(1)
	}

	logger.Info("Redis缓存功能测试通过")
	logger.Info("成功连接到Redis缓存服务器 %s:%d (数据库: %d)", redisHost, redisPort, redisDB)

	// 创建服务实例
	authService := service.NewAuthService(
		database.GetDB(),
		cacheInstance,
	)

	// 创建权限服务实例
	permissionService := service.NewPermissionService(database.GetDB(), cacheInstance)

	// 启动Redis健康检查
	if err := cacheInstance.StartHealthMonitor(30 * time.Second); err != nil {
		logger.Warn("启动Redis健康监控失败: %v", err)
	}

	// 从viper获取心跳配置
	clientInterval := viper.GetString("heartbeat.client_interval")
	serverTimeout := viper.GetString("heartbeat.server_timeout")
	cleanupInterval := viper.GetString("heartbeat.cleanup_interval")

	logger.Info("心跳配置: 客户端间隔=%s, 服务器超时=%s, 清理间隔=%s",
		clientInterval, serverTimeout, cleanupInterval)

	// 解析时间间隔
	serverTimeoutDuration, err := time.ParseDuration(serverTimeout)
	if err != nil {
		logger.Fatal("解析服务器超时时间失败: %v", err)
	}

	cleanupIntervalDuration, err := time.ParseDuration(cleanupInterval)
	if err != nil {
		logger.Fatal("解析清理间隔时间失败: %v", err)
	}

	// 创建心跳服务实例
	heartbeatService := service.NewHeartbeatService(
		serverTimeoutDuration,
		cleanupIntervalDuration,
		metricsInstance,
		cacheInstance,
	)

	// 创建签到抽奖服务实例（使用Hash缓存）
	redisAddr := fmt.Sprintf("%s:%d", redisHost, redisPort)
	luckyService := service.NewLuckyService(cacheInstance, permissionService, redisAddr, redisPassword, redisDB)

	// 创建风控服务实例
	riskService := service.NewRiskControlService(database.GetDB(), cacheInstance)

	// 创建统计服务实例
	statsService := service.NewStatsService(database.GetDB(), cacheInstance, authService, heartbeatService, riskService)

	// 启动统计数据收集器和清理任务
	statsService.StartStatsCollector()
	statsService.StartStatsCleanup()

	// 创建公告服务实例
	announcementService := service.NewAnnouncementService(database.GetDB(), cacheInstance)

	// 创建公告推送服务实例
	logger.Info("正在创建公告推送服务... Redis: %s:%d, DB: %d", redisHost, redisPort, redisDB)
	announcementPushService, err := service.NewAnnouncementPushService(redisHost, redisPort, redisPassword, redisDB, authService)
	if err != nil {
		logger.Error("创建公告推送服务失败: %v", err)
		logger.Error("请检查Redis配置是否正确")
		logger.Error("公告推送功能将不可用，但服务器将继续运行")
	} else {
		logger.Info("公告推送服务初始化成功")
		logger.Info("公告推送服务已启动，正在监听Redis频道: announcement_update")
		// 确保程序退出时关闭推送服务
		defer func() {
			logger.Info("正在关闭公告推送服务...")
			announcementPushService.Close()
		}()
	}

	// 创建更新配置服务实例
	updateService := service.NewUpdateService(database.GetDB(), cacheInstance)
	updateInitService := service.NewUpdateInitService(updateService)

	// 初始化更新配置缓存
	if err := updateInitService.InitializeCache(); err != nil {
		logger.Error("Failed to initialize update config cache: %v", err)
	}

	// 创建处理器实例
	authHandler := handler.NewAuthHandler(authService, permissionService)
	heartbeatHandler := handler.NewHeartbeatHandler(authService, heartbeatService, updateService)
	luckyHandler := handler.NewLuckyHandler(luckyService, authService, permissionService)
	announcementHandler := handler.NewAnnouncementHandler(announcementService, authService)
	updateHandler := handler.NewUpdateHandler(updateService)
	webhookHandler := handler.NewWebhookHandler(announcementService, authService)

	// 启动HTTP服务器（用于接收Webhook）
	go startHTTPServer(webhookHandler)

	// 创建UDP服务器
	addr := net.UDPAddr{
		Port: viper.GetInt("server.port"),
		IP:   net.ParseIP(viper.GetString("server.host")),
	}

	conn, err := net.ListenUDP("udp", &addr)
	if err != nil {
		logger.Fatal("Failed to start UDP server: %v", err)
	}
	defer conn.Close()

	logger.Info("UDP server started on %s:%d", viper.GetString("server.host"), viper.GetInt("server.port"))

	// 创建网络处理器
	networkHandler := binary.NewNetworkHandler()

	// 处理消息
	buffer := make([]byte, 65536) // 增大缓冲区以支持更大的消息
	for {
		n, remoteAddr, err := conn.ReadFromUDP(buffer)
		if err != nil {
			logger.Error("Error reading from UDP: %v", err)
			continue
		}

		// 验证是否为二进制协议
		if err := networkHandler.ValidateMessage(buffer[:n]); err != nil {
			logger.Error("Invalid message from %s: %v", remoteAddr.IP.String(), err)
			continue
		}

		// 处理二进制协议消息
		binaryMsg, parseErr := networkHandler.ProcessBinaryMessage(buffer[:n])
		if parseErr != nil {
			logger.Error("Error parsing binary message from %s: %v", remoteAddr.IP.String(), parseErr)
			continue
		}

		// 根据消息类型处理
		var responseData []byte
		var handleErr error

		switch binaryMsg.Header.MsgType {
		case binary.MsgTypeLogin:
			responseData, handleErr = handleLoginRequest(binaryMsg, authHandler, permissionService, riskService, remoteAddr)
		case binary.MsgTypeHeartbeat:
			responseData, handleErr = handleHeartbeatRequest(binaryMsg, heartbeatHandler)
		case binary.MsgTypeLogout:
			responseData, handleErr = handleLogoutRequest(binaryMsg, authHandler, remoteAddr)
		case binary.MsgTypeGetDeviceHistory:
			// 功能未实现
			networkHandler := binary.NewNetworkHandler()
			responseData, handleErr = networkHandler.CreateErrorResponse(binary.ErrorCodeServerError, "GetDeviceHistory not implemented")
		case binary.MsgTypeGetActiveDevices:
			// 功能未实现
			networkHandler := binary.NewNetworkHandler()
			responseData, handleErr = networkHandler.CreateErrorResponse(binary.ErrorCodeServerError, "GetActiveDevices not implemented")
		case binary.MsgTypeLuckyDraw:
			responseData, handleErr = handleLuckyDrawRequest(binaryMsg, luckyHandler, remoteAddr)
		case binary.MsgTypeLuckyStatus:
			responseData, handleErr = handleLuckyStatusRequest(binaryMsg, luckyHandler, remoteAddr)
		case binary.MsgTypeCDKeyActivate:
			responseData, handleErr = handleCDKeyActivateRequest(binaryMsg, luckyHandler, remoteAddr)
		case binary.MsgTypeAnnouncementDetail:
			responseData, handleErr = handleAnnouncementDetailRequest(binaryMsg, announcementHandler, remoteAddr)
		case binary.MsgTypeUpdateConfig:
			responseData, handleErr = handleUpdateConfigRequest(binaryMsg, updateHandler, remoteAddr)
		case binary.MsgTypeAnnouncementConfig:
			responseData, handleErr = handleAnnouncementConfigRequest(binaryMsg, announcementHandler, remoteAddr)
		default:
			logger.Warn("Unknown message type: 0x%02X from %s", binaryMsg.Header.MsgType, remoteAddr.IP.String())
			// 发送错误响应
			responseData, _ = networkHandler.CreateErrorResponse(binary.ErrorCodeInvalidFormat, "Unknown message type")
		}

		if handleErr != nil {
			logger.Error("Error handling message: %v", handleErr)
			// 发送错误响应
			responseData, _ = networkHandler.CreateErrorResponse(binary.ErrorCodeServerError, handleErr.Error())
		}

		if responseData != nil {
			_, err = conn.WriteToUDP(responseData, remoteAddr)
			if err != nil {
				logger.Error("Error sending response: %v", err)
			}
		}
	}
}

// handleLuckyDrawRequest 处理签到抽奖请求
func handleLuckyDrawRequest(binaryMsg *binary.Message, luckyHandler *handler.LuckyHandler, remoteAddr *net.UDPAddr) ([]byte, error) {
	// 解析签到抽奖请求
	luckyReq, err := binaryMsg.GetLuckyDrawRequest()
	if err != nil {
		return nil, fmt.Errorf("failed to parse lucky draw request: %w", err)
	}

	// 直接调用签到抽奖处理器
	return luckyHandler.HandleLuckyDrawDirect(luckyReq.Token, remoteAddr.IP.String(), "")
}

// handleLuckyStatusRequest 处理获取签到状态请求
func handleLuckyStatusRequest(binaryMsg *binary.Message, luckyHandler *handler.LuckyHandler, remoteAddr *net.UDPAddr) ([]byte, error) {
	// 解析签到状态请求
	statusReq, err := binaryMsg.GetLuckyStatusRequest()
	if err != nil {
		return nil, fmt.Errorf("failed to parse lucky status request: %w", err)
	}

	// 直接调用签到状态处理器
	return luckyHandler.HandleLuckyStatusDirect(statusReq.Token)
}

// handleLoginRequest 处理登录请求
func handleLoginRequest(binaryMsg *binary.Message, authHandler *handler.AuthHandler, permissionService *service.PermissionService, riskService *service.RiskControlService, remoteAddr *net.UDPAddr) ([]byte, error) {
	// 解析登录请求
	loginReq, err := binaryMsg.GetLoginRequest()
	if err != nil {
		return nil, fmt.Errorf("failed to parse login request: %w", err)
	}

	// 转换QQ号为int64进行风控检查
	qqNumberInt, err := strconv.ParseInt(loginReq.QQNumber, 10, 64)
	if err != nil {
		networkHandler := binary.NewNetworkHandler()
		return networkHandler.CreateErrorResponse(binary.ErrorCodeInvalidFormat, "无效的QQ号格式")
	}

	// 执行风控检查
	clientIP := remoteAddr.IP.String()
	if err := riskService.CheckLoginRisk(qqNumberInt, loginReq.DeviceFingerprint, clientIP); err != nil {
		logger.Warn("风控检查失败: QQ=%d, Device=%s, IP=%s, Error=%v",
			qqNumberInt, loginReq.DeviceFingerprint, clientIP, err)

		// 创建风控错误响应
		networkHandler := binary.NewNetworkHandler()
		return networkHandler.CreateErrorResponse(binary.ErrorCodeUnauthorized, err.Error())
	}

	// 风控检查通过，调用认证服务进行登录
	user, err := authHandler.LoginDirect(loginReq.QQNumber, loginReq.DeviceFingerprint, loginReq, clientIP)
	if err != nil {
		networkHandler := binary.NewNetworkHandler()
		return networkHandler.CreateErrorResponse(binary.ErrorCodeServerError, err.Error())
	}

	// 获取权限过期时间（使用统一的权限计算方法）
	permissionExpiration, err := permissionService.GetUserPermissionExpiration(user.UID, "client")
	if err != nil {
		logger.Warn("获取用户权限过期时间失败: UID=%d, Error=%v", user.UID, err)
		permissionExpiration = 0 // 无权限
	}

	// 获取插件信息
	pluginVersion, pluginUrl, err := getPluginInfo()
	if err != nil {
		logger.Warn("获取插件信息失败: %v", err)
		// 失败时使用默认值
		pluginVersion = ""
		pluginUrl = ""
	}

	// 创建成功响应数据（包含插件信息）
	responseData := map[string]interface{}{
		"token":                 user.Token,
		"permission":            user.Permission,      // 最终权限：服务端计算的结果
		"permission_expiration": permissionExpiration, // 权限过期时间：-1=永久，0=无权限，>0=具体时间戳
		"plugin_version":        pluginVersion,        // 插件版本
		"plugin_url":            pluginUrl,            // 插件下载URL
	}

	networkHandler := binary.NewNetworkHandler()
	return networkHandler.CreateSuccessResponse(binary.MsgTypeLoginResponse, responseData)
}

// getPluginInfo 获取插件版本和插件URL
func getPluginInfo() (string, string, error) {
	// 从数据库获取更新配置中的插件信息
	var config model.UpdateConfig

	// 尝试多个可能的应用名称
	appNames := []string{"bns-helper", "BnsHelper", "bns_helper"}
	var err error

	for _, appName := range appNames {
		err = database.GetDB().Where("name = ? AND is_active = ?", appName, true).First(&config).Error
		if err == nil {
			logger.Debug("找到更新配置，应用名称: %s", appName)
			break
		}
		if err != gorm.ErrRecordNotFound {
			// 如果是其他错误（非记录不存在），直接返回
			return "", "", fmt.Errorf("failed to get plugin config: %v", err)
		}
	}

	if err == gorm.ErrRecordNotFound {
		logger.Warn("未找到任何活跃的更新配置，尝试的应用名称: %v", appNames)
		// 如果没有找到配置，返回空值
		return "", "", nil
	}

	logger.Info("插件信息获取成功: PluginVersion=%s, PluginURL=%s",
		config.PluginVersion, config.PluginURL)

	return config.PluginVersion, config.PluginURL, nil
}

// handleHeartbeatRequest 处理心跳请求
func handleHeartbeatRequest(binaryMsg *binary.Message, heartbeatHandler *handler.HeartbeatHandler) ([]byte, error) {
	// 解析心跳请求
	heartbeatReq, err := binaryMsg.GetHeartbeatRequest()
	if err != nil {
		return nil, fmt.Errorf("failed to parse heartbeat request: %w", err)
	}

	// 直接调用心跳处理器
	_, onlineCount, forceUpdate, err := heartbeatHandler.HandleHeartbeatDirect(heartbeatReq.Token, heartbeatReq.Version)
	if err != nil {
		// 创建错误响应
		networkHandler := binary.NewNetworkHandler()
		return networkHandler.CreateErrorResponse(binary.ErrorCodeServerError, err.Error())
	}

	// 创建包含在线用户数量和强制更新标志的心跳响应
	networkHandler := binary.NewNetworkHandler()
	return networkHandler.CreateHeartbeatResponseWithUpdateCheck(onlineCount, forceUpdate)
}

// handleLogoutRequest 处理登出请求
func handleLogoutRequest(binaryMsg *binary.Message, authHandler *handler.AuthHandler, remoteAddr *net.UDPAddr) ([]byte, error) {
	// 解析注销请求
	logoutReq, err := binaryMsg.GetLogoutRequest()
	if err != nil {
		networkHandler := binary.NewNetworkHandler()
		return networkHandler.CreateLogoutResponse(false, binary.ErrorCodeServerError, fmt.Sprintf("failed to parse logout request: %v", err))
	}

	logger.Debug("收到注销请求: Token=%s, 客户端IP=%s", logoutReq.Token, remoteAddr.IP.String())

	// 调用认证服务进行注销
	err = authHandler.LogoutDirect(logoutReq.Token)
	if err != nil {
		logger.Error("注销失败: Token=%s, 错误=%v", logoutReq.Token, err)
		networkHandler := binary.NewNetworkHandler()
		return networkHandler.CreateLogoutResponse(false, binary.ErrorCodeServerError, fmt.Sprintf("logout failed: %v", err))
	}

	logger.Info("注销成功: Token=%s, 客户端IP=%s", logoutReq.Token, remoteAddr.IP.String())

	// 创建成功响应
	networkHandler := binary.NewNetworkHandler()
	return networkHandler.CreateLogoutResponse(true, 0, "")
}

// handleCDKeyActivateRequest 处理CDKEY激活请求
func handleCDKeyActivateRequest(binaryMsg *binary.Message, luckyHandler *handler.LuckyHandler, remoteAddr *net.UDPAddr) ([]byte, error) {
	// 解析CDKEY激活请求
	cdkeyReq, err := binaryMsg.GetCDKeyActivateRequest()
	if err != nil {
		return nil, fmt.Errorf("failed to parse cdkey activate request: %w", err)
	}

	// 调用CDKEY激活处理器
	return luckyHandler.HandleCDKeyActivate(cdkeyReq.Token, cdkeyReq.CDKey, cdkeyReq.IsVerified, remoteAddr.IP.String())
}

// handleAnnouncementDetailRequest 处理获取公告详情请求
func handleAnnouncementDetailRequest(binaryMsg *binary.Message, handler *handler.AnnouncementHandler, remoteAddr *net.UDPAddr) ([]byte, error) {
	logger.Debug("处理公告详情请求，来自: %s", remoteAddr.IP.String())

	// 解析公告详情请求
	req, err := binaryMsg.GetAnnouncementDetailRequest()
	if err != nil {
		logger.Error("解析公告详情请求失败: %v", err)
		response := &binary.AnnouncementDetailResponse{
			ErrorCode: binary.ErrorCodeInvalidFormat,
			ErrorMsg:  "Invalid request format",
		}
		responseMsg := binary.NewAnnouncementDetailResponseMessage(response)
		return responseMsg.Encode()
	}

	if req.Token == "" {
		response := &binary.AnnouncementDetailResponse{
			ErrorCode: binary.ErrorCodeUnauthorized,
			ErrorMsg:  "Missing token",
		}
		responseMsg := binary.NewAnnouncementDetailResponseMessage(response)
		return responseMsg.Encode()
	}

	if req.AnnouncementID == 0 {
		response := &binary.AnnouncementDetailResponse{
			ErrorCode: binary.ErrorCodeInvalidFormat,
			ErrorMsg:  "Missing announcement ID",
		}
		responseMsg := binary.NewAnnouncementDetailResponseMessage(response)
		return responseMsg.Encode()
	}

	// 暂时返回错误，表示功能未完全实现
	// TODO: 需要实现完整的公告详情获取逻辑，使用 req.AnnouncementID
	response := &binary.AnnouncementDetailResponse{
		ErrorCode: binary.ErrorCodeServerError,
		ErrorMsg:  "Feature not fully implemented",
	}
	responseMsg := binary.NewAnnouncementDetailResponseMessage(response)
	return responseMsg.Encode()
}

// handleUpdateConfigRequest 处理更新配置请求
func handleUpdateConfigRequest(binaryMsg *binary.Message, handler *handler.UpdateHandler, remoteAddr *net.UDPAddr) ([]byte, error) {
	// 解析更新配置请求
	updateReq, err := binaryMsg.GetUpdateConfigRequest()
	if err != nil {
		return nil, fmt.Errorf("failed to parse update config request: %w", err)
	}

	// 调用更新配置处理器
	return handler.HandleUpdateConfig(
		updateReq.Token,
		updateReq.AppType,
		updateReq.Version,
		remoteAddr.IP.String(),
	)
}

// handleAnnouncementConfigRequest 处理公告配置请求
func handleAnnouncementConfigRequest(binaryMsg *binary.Message, handler *handler.AnnouncementHandler, remoteAddr *net.UDPAddr) ([]byte, error) {
	logger.Debug("处理公告配置请求，来自: %s", remoteAddr.IP.String())

	// 解析公告配置请求
	req, err := binaryMsg.GetAnnouncementConfigRequest()
	if err != nil {
		logger.Error("解析公告配置请求失败: %v", err)
		response := &binary.AnnouncementConfigResponse{
			ErrorCode: binary.ErrorCodeInvalidFormat,
			ErrorMsg:  "Invalid request format",
		}
		responseMsg := binary.NewAnnouncementConfigResponseMessage(response)
		return responseMsg.Encode()
	}

	if req.Token == "" {
		response := &binary.AnnouncementConfigResponse{
			ErrorCode: binary.ErrorCodeUnauthorized,
			ErrorMsg:  "Missing token",
		}
		responseMsg := binary.NewAnnouncementConfigResponseMessage(response)
		return responseMsg.Encode()
	}

	// 暂时返回一个简单的配置响应，表示没有更新
	response := &binary.AnnouncementConfigResponse{
		ErrorCode:      binary.ErrorCodeSuccess,
		CurrentVersion: 1,
		HasUpdate:      false,
	}
	responseMsg := binary.NewAnnouncementConfigResponseMessage(response)
	return responseMsg.Encode()
}

// initConfig 初始化配置
func initConfig() {
	// 设置配置文件路径
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath("config")
	viper.AddConfigPath(".")

	// 读取环境变量
	viper.AutomaticEnv()

	// 读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); ok {
			// 配置文件不存在，创建默认配置
			fmt.Printf("配置文件不存在，创建默认配置...\n")
			createDefaultConfig()
		} else {
			fmt.Printf("Error reading config file: %v\n", err)
			os.Exit(1)
		}
	}
}

// createDefaultConfig 创建默认配置文件
func createDefaultConfig() {
	// 确保配置目录存在
	if err := os.MkdirAll("config", 0755); err != nil {
		fmt.Printf("Error creating config directory: %v\n", err)
		os.Exit(1)
	}

	// 创建默认配置
	viper.SetDefault("database.driver", "mysql")
	viper.SetDefault("database.host", "localhost")
	viper.SetDefault("database.port", 3306)
	viper.SetDefault("database.username", "root")
	viper.SetDefault("database.password", "root")
	viper.SetDefault("database.dbname", "bns")
	viper.SetDefault("database.charset", "utf8mb4")
	viper.SetDefault("database.parseTime", true)
	viper.SetDefault("database.loc", "Local")
	viper.SetDefault("database.maxIdleConns", 10)
	viper.SetDefault("database.maxOpenConns", 100)
	viper.SetDefault("database.connMaxLifetime", 3600)

	viper.SetDefault("server.host", "0.0.0.0")
	viper.SetDefault("server.port", 8081)

	// HTTP服务器配置
	viper.SetDefault("http.host", "0.0.0.0")
	viper.SetDefault("http.port", 8080)

	// Redis 配置
	viper.SetDefault("redis.host", "localhost")
	viper.SetDefault("redis.port", 6379)
	viper.SetDefault("redis.password", "")
	viper.SetDefault("redis.db", 4)

	// 日志配置
	viper.SetDefault("log.level", "INFO")
	viper.SetDefault("log.console", true)
	viper.SetDefault("log.file", false)
	viper.SetDefault("log.file_path", "logs/server.log")

	// 心跳配置
	viper.SetDefault("heartbeat.client_interval", "25m")
	viper.SetDefault("heartbeat.server_timeout", "30m")
	viper.SetDefault("heartbeat.cleanup_interval", "5m")

	// 保存配置文件
	if err := viper.WriteConfigAs(filepath.Join("config", "config.yaml")); err != nil {
		fmt.Printf("Error writing config file: %v\n", err)
		os.Exit(1)
	}
}

// startHTTPServer 启动HTTP服务器用于接收Webhook
func startHTTPServer(webhookHandler *handler.WebhookHandler) {
	logger.Info("正在启动HTTP Webhook服务器...")

	// 创建路由器
	router := mux.NewRouter()

	// 添加中间件
	router.Use(loggingMiddleware)
	router.Use(corsMiddleware)

	// 注册路由
	router.HandleFunc("/webhook/announcement", webhookHandler.HandleAnnouncementWebhook).Methods("POST")
	router.HandleFunc("/health", webhookHandler.HandleHealthCheck).Methods("GET")

	// 获取HTTP服务器配置
	httpHost := viper.GetString("http.host")
	httpPort := viper.GetInt("http.port")

	if httpHost == "" {
		httpHost = "0.0.0.0"
	}
	if httpPort == 0 {
		httpPort = 8080
	}

	addr := fmt.Sprintf("%s:%d", httpHost, httpPort)

	// 创建HTTP服务器
	server := &http.Server{
		Addr:         addr,
		Handler:      router,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  60 * time.Second,
	}

	logger.Info("HTTP Webhook服务器启动成功，监听地址: %s", addr)
	logger.Info("Webhook端点:")
	logger.Info("  POST /webhook/announcement - 接收公告推送")
	logger.Info("  GET  /health - 健康检查")

	// 启动服务器
	if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
		logger.Error("HTTP服务器启动失败: %v", err)
	}
}

// loggingMiddleware HTTP请求日志中间件
func loggingMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		start := time.Now()

		// 记录请求
		logger.Debug("HTTP请求: %s %s 来自 %s", r.Method, r.URL.Path, r.RemoteAddr)

		// 处理请求
		next.ServeHTTP(w, r)

		// 记录响应时间
		duration := time.Since(start)
		logger.Debug("HTTP响应: %s %s 耗时 %v", r.Method, r.URL.Path, duration)
	})
}

// corsMiddleware CORS中间件
func corsMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 设置CORS头
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")

		// 处理预检请求
		if r.Method == "OPTIONS" {
			w.WriteHeader(http.StatusOK)
			return
		}

		next.ServeHTTP(w, r)
	})
}
