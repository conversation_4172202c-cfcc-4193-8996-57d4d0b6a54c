<?php
// +----------------------------------------------------------------------
// | Manage应用路由配置
// +----------------------------------------------------------------------

use think\facade\Route;

// 管理后台基础路由
Route::rule('/', 'Manage/Index');
Route::rule('/login', 'Manage/Login');
Route::rule('/logout', 'Manage/Logout');
Route::rule('/help', 'Manage/Help');
Route::rule('/choose', 'Manage/Choose');
Route::rule('/liars', 'Manage/Liars');
Route::rule('/liarpost', 'Manage/LiarPost');
Route::rule('/changelog', 'Manage/ChangeLog');
Route::rule('/sendcode', 'Manage/SendCode');
Route::rule('/register', 'Manage/Register');
Route::rule('/image', 'Manage/Image');

// 用户中心路由
Route::rule('/center', 'manage/Center/main');
Route::rule('/draw', 'manage/Center/Draw');
Route::rule('/profile', 'manage/Profile/main');

// 管理员功能路由组
Route::group('admin', function() {
    Route::rule('/check', 'Admin/check');
    Route::rule('/cdkey', 'Admin/cdkey');
    Route::rule('/users', 'Admin/users');
    Route::rule('/question', 'Admin/question');
    Route::rule('/profiles', 'Admin/profiles');
    Route::rule('/downloads', 'Admin/downloads');
    Route::rule('/deleteDownload', 'Admin/deleteDownload');

    // 风控管理路由
    Route::group('risk', function() {
        Route::rule('/', 'manage.RiskControl/index');
        Route::rule('/events', 'manage.RiskControl/events');
        Route::rule('/config', 'manage.RiskControl/config');
    });

    // 签到管理路由
    Route::group('signin', function() {
        Route::rule('/', 'manage.SigninManage/index');
    });

    // 在线用户统计管理路由
    Route::group('online', function() {
        Route::rule('/', 'manage.OnlineDashboard/index');
    });

    // 公告管理路由
    Route::group('announcement', function() {
        Route::rule('/', 'manage.Announcement/index');
        Route::rule('/add', 'manage.Announcement/add');
        Route::rule('/edit/:id', 'manage.Announcement/edit');
    });

    // 更新配置管理路由
    Route::group('update-config', function() {
        Route::rule('/', 'manage.UpdateConfig/index');
        Route::rule('/edit', 'manage.UpdateConfig/edit');
        Route::rule('/groups/:app_name', 'manage.UpdateConfig/groups');
        Route::rule('/getForceUpdateStatus', 'api.UpdateConfig/getForceUpdateStatus');
        Route::rule('/setForceUpdate', 'api.UpdateConfig/setForceUpdate');
    });
});

// API接口路由组
Route::group('api', function() {
    // 在线统计API
    Route::rule('/online/current', 'api/OnlineStats/current');
    Route::rule('/online/health', 'api/OnlineStats/health');

    // 风控API
    Route::rule('/risk/record', 'api/RiskControl/recordEvent');
    Route::rule('/risk/stats', 'api/RiskControl/getStats');
    Route::rule('/risk/events', 'api/RiskControl/getEvents');
    Route::rule('/risk/update', 'api/RiskControl/updateEventStatus');
    Route::rule('/risk/check', 'api/RiskControl/checkDeviceRisk');
    Route::rule('/risk/health', 'api/RiskControl/health');

    // 更新配置API
    Route::rule('/update', 'api/UpdateConfig/index');
    Route::rule('/update/getForceUpdateStatus', 'api/UpdateConfig/getForceUpdateStatus');
    Route::rule('/update/setForceUpdate', 'api/UpdateConfig/setForceUpdate');

    // 公告API
    Route::rule('/announcement/list', 'api/Announcement/list');
    Route::rule('/announcement/checkUpdate', 'api/Announcement/checkUpdate');
    Route::rule('/announcement/detail', 'api/Announcement/detail');
    Route::rule('/announcement/stats', 'api/Announcement/stats');
    Route::rule('/announcement/health', 'api/Announcement/health');
});
