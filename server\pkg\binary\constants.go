package binary

// 协议常量定义
const (
	// 协议魔数和版本
	ProtocolMagic   = 0xDA
	ProtocolVersion = 0x01

	// 消息头大小
	HeaderSize = 16 // 固定16字节

	// 标志位定义
	FlagNeedResponse = 0x01 // 需要响应
	FlagCompressed   = 0x02 // 已压缩
	FlagEncrypted    = 0x04 // 已加密

	// 限制常量
	MaxMessageSize = 65536 // 最大消息大小 64KB
)

// 消息类型定义
const (
	MsgTypeLogin                    = 0x01 // 登录请求
	MsgTypeLoginResponse            = 0x81 // 登录响应
	MsgTypeHeartbeat                = 0x02 // 心跳请求
	MsgTypeHeartbeatResponse        = 0x82 // 心跳响应
	MsgTypeLogout                   = 0x03 // 登出请求
	MsgTypeLogoutResponse           = 0x83 // 登出响应
	MsgTypeGetDeviceHistory         = 0x04 // 获取设备历史
	MsgTypeGetDeviceHistoryResponse = 0x84 // 设备历史响应
	MsgTypeGetActiveDevices         = 0x05 // 获取活跃设备
	MsgTypeGetActiveDevicesResponse = 0x85 // 活跃设备响应
	MsgTypeLuckyDraw                = 0x06 // 签到抽奖请求
	MsgTypeLuckyDrawResponse        = 0x86 // 签到抽奖响应
	MsgTypeLuckyStatus              = 0x07 // 获取签到状态
	MsgTypeLuckyStatusResponse      = 0x87 // 签到状态响应
	MsgTypeCDKeyActivate            = 0x08 // CDKEY激活请求
	MsgTypeCDKeyActivateResponse    = 0x88 // CDKEY激活响应
	MsgTypeGetActivityInfo          = 0x09 // 获取活动信息请求
	MsgTypeGetActivityInfoResponse  = 0x89 // 获取活动信息响应

	// 扩展功能 (0x20-0x2F)
	MsgTypeUpdateConfig               = 0x20 // 获取更新配置请求
	MsgTypeUpdateConfigResponse       = 0xA0 // 更新配置响应
	MsgTypeAnnouncementConfig         = 0x21 // 获取公告配置请求
	MsgTypeAnnouncementConfigResponse = 0xA1 // 公告配置响应
	MsgTypeAnnouncementDetail         = 0x22 // 获取公告详情请求
	MsgTypeAnnouncementDetailResponse = 0xA2 // 公告详情响应
	MsgTypeError                      = 0xFF // 错误响应
)

// 字段类型定义
const (
	FieldTypeClientVersion  = 0x1 // 客户端版本 (string)
	FieldTypeDownloadURL    = 0x2 // 下载链接 (string)
	FieldTypeExecutablePath = 0x3 // 可执行文件路径 (string)
	FieldTypePluginVersion  = 0x4 // 插件版本 (string)
	FieldTypePluginURL      = 0x5 // 插件下载链接 (string)
	FieldTypeGroups         = 0x6 // 群组信息 (long array)
)

// 错误码定义
const (
	ErrorCodeSuccess          = 0
	ErrorCodeInvalidFormat    = 1001 // 无效格式
	ErrorCodeInvalidRequest   = 1001 // 无效请求（别名）
	ErrorCodeInvalidQQ        = 1002 // 无效QQ号
	ErrorCodeInvalidDevice    = 1003 // 无效设备
	ErrorCodeUnauthorized     = 1004 // 未授权
	ErrorCodePermissionDenied = 1004 // 权限不足（别名）
	ErrorCodeTokenExpired     = 1005 // Token过期
	ErrorCodeNotFound         = 1006 // 资源不存在
	ErrorCodeRateLimit        = 1007 // 频率限制
	ErrorCodeServerError      = 2001 // 服务器错误
	ErrorCodeDatabaseError    = 2002 // 数据库错误
	ErrorCodeCacheError       = 2003 // 缓存错误
)

// 消息类型到字符串的映射
var MsgTypeNames = map[uint8]string{
	MsgTypeLogin:                    "Login",
	MsgTypeLoginResponse:            "LoginResponse",
	MsgTypeHeartbeat:                "Heartbeat",
	MsgTypeHeartbeatResponse:        "HeartbeatResponse",
	MsgTypeLogout:                   "Logout",
	MsgTypeLogoutResponse:           "LogoutResponse",
	MsgTypeGetDeviceHistory:         "GetDeviceHistory",
	MsgTypeGetDeviceHistoryResponse: "GetDeviceHistoryResponse",
	MsgTypeGetActiveDevices:         "GetActiveDevices",
	MsgTypeGetActiveDevicesResponse: "GetActiveDevicesResponse",
	MsgTypeLuckyDraw:                "LuckyDraw",
	MsgTypeLuckyDrawResponse:        "LuckyDrawResponse",
	MsgTypeLuckyStatus:              "LuckyStatus",
	MsgTypeLuckyStatusResponse:      "LuckyStatusResponse",
	MsgTypeCDKeyActivate:            "CDKeyActivate",
	MsgTypeCDKeyActivateResponse:    "CDKeyActivateResponse",

	MsgTypeUpdateConfig:               "UpdateConfig",
	MsgTypeUpdateConfigResponse:       "UpdateConfigResponse",
	MsgTypeAnnouncementConfig:         "AnnouncementConfig",
	MsgTypeAnnouncementConfigResponse: "AnnouncementConfigResponse",
	MsgTypeAnnouncementDetail:         "AnnouncementDetail",
	MsgTypeAnnouncementDetailResponse: "AnnouncementDetailResponse",
	MsgTypeError:                      "Error",
}

// 字段类型到字符串的映射
var FieldTypeNames = map[uint8]string{
	FieldTypeClientVersion:  "ClientVersion",
	FieldTypeDownloadURL:    "DownloadURL",
	FieldTypeExecutablePath: "ExecutablePath",
	FieldTypePluginVersion:  "PluginVersion",
	FieldTypePluginURL:      "PluginURL",
	FieldTypeGroups:         "Groups",
}

// 错误码到消息的映射
var ErrorMessages = map[uint32]string{
	ErrorCodeSuccess:       "Success",
	ErrorCodeInvalidFormat: "Invalid message format", // 1001 (also covers ErrorCodeInvalidRequest)
	ErrorCodeInvalidQQ:     "Invalid QQ number",
	ErrorCodeInvalidDevice: "Invalid device information",
	ErrorCodeUnauthorized:  "Unauthorized access", // 1004 (also covers ErrorCodePermissionDenied)
	ErrorCodeTokenExpired:  "Token expired",
	ErrorCodeNotFound:      "Resource not found",
	ErrorCodeRateLimit:     "Rate limit exceeded",
	ErrorCodeServerError:   "Internal server error",
	ErrorCodeDatabaseError: "Database error",
	ErrorCodeCacheError:    "Cache error",
}

// IsResponseType 检查是否为响应类型
func IsResponseType(msgType uint8) bool {
	return (msgType & 0x80) != 0
}

// GetResponseType 获取对应的响应类型
func GetResponseType(requestType uint8) uint8 {
	return requestType | 0x80
}

// GetRequestType 获取对应的请求类型
func GetRequestType(responseType uint8) uint8 {
	return responseType & 0x7F
}
