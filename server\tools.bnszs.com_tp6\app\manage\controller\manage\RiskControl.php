<?php

namespace app\manage\controller\manage;

use app\common\controller\BaseController;
use think\App;
use think\Request;
use think\facade\Cache;
use think\facade\Db;
use think\facade\View;
use app\manage\model\UserAdmin as UserAdmin;

class RiskControl extends BaseController
{
    private $admin = 0;
    private $adminInfo = null;
    protected $request;

    public function __construct(App $app, Request $request) {
        parent::__construct($app, $request);
        $this->request = $request;

        $admin = $this->admin = $_SESSION['admin'] ?? null;
        if($admin == 0 || $admin == null) {
            if ($request->isAjax()) {
                $response = json([
                    'code' => 0,
                    'msg' => '请先登录管理员账户',
                    'redirect' => '/manage/login?type=admin&callback=/manage'
                ]);
                $response->send();
                exit;
            } else {
                header("Location:/manage/login?type=admin&callback=/manage");
                die;
            }
        }

        // 获取管理员信息
        $this->adminInfo = UserAdmin::where('uid', $admin)->find();
        if(!$this->adminInfo) {
            if ($request->isAjax()) {
                $response = json([
                    'code' => 0,
                    'msg' => '管理员信息不存在',
                    'redirect' => '/manage/login?type=admin&callback=/manage'
                ]);
                $response->send();
                exit;
            } else {
                header("Location:/manage/login?type=admin&callback=/manage");
                die;
            }
        }

        // 检查风控权限
        if(!$this->checkRiskControlPermission()) {
            abort(403, '您没有风控管理权限');
        }
    }

    /**
     * 检查风控权限
     */
    private function checkRiskControlPermission() {
        // 超级管理员拥有所有权限
        if($this->adminInfo['super'] == 1) {
            return true;
        }

        // 检查是否有风控相关权限 (14, 15, 16)
        $powers = explode(',', $this->adminInfo['power']);
        $riskControlPowers = [14, 15, 16]; // 风控查看、管理、配置权限
        
        foreach($riskControlPowers as $power) {
            if(in_array($power, $powers)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 检查特定权限
     */
    private function checkPermission($powerID) {
        if($this->adminInfo['super'] == 1) {
            return true;
        }
        
        $powers = explode(',', $this->adminInfo['power']);
        return in_array($powerID, $powers);
    }

    /**
     * 风控仪表板
     */
    public function index() {
        if($this->request->isPost()) {
            // 处理POST请求
            $action = $this->request->param('action', '');

            switch($action) {
                case 'processEvent':
                    return $this->processEvent();
                case 'batchProcessUsers':
                    return $this->batchProcessUsers();
                case 'saveConfig':
                    return $this->saveConfig();
                default:
                    return json(['code' => 0, 'msg' => '未知操作']);
            }
        }

        try {
            // 获取今日统计
            $today = date('Y-m-d');
            $todayStart = $today . ' 00:00:00';
            $todayEnd = $today . ' 23:59:59';

            // 用户状态统计（使用实际存在的表）
            $auditUsers = Db::name('user')->where('status', 3)->count(); // 审核状态用户
            $bannedUsers = Db::name('user')->where('status', '>', 0)->count(); // 所有异常状态用户
            $totalUsers = Db::name('user')->count(); // 总用户数
            $normalUsers = Db::name('user')->where('status', 0)->count(); // 正常用户

            // 尝试获取风险事件统计（如果表不存在则使用默认值）
            $totalEvents = 0;
            $pendingEvents = 0;
            $highSeverityEvents = 0;
            $mediumSeverityEvents = 0;
            $recentEvents = [];

            try {
                $totalEvents = Db::name('risk_events')
                    ->where('created_at', 'between', [$todayStart, $todayEnd])
                    ->count();

                $pendingEvents = Db::name('risk_events')
                    ->where('created_at', 'between', [$todayStart, $todayEnd])
                    ->where('status', 'pending')
                    ->count();

                $highSeverityEvents = Db::name('risk_events')
                    ->where('created_at', 'between', [$todayStart, $todayEnd])
                    ->where('severity', 'high')
                    ->count();

                $mediumSeverityEvents = Db::name('risk_events')
                    ->where('created_at', 'between', [$todayStart, $todayEnd])
                    ->where('severity', 'medium')
                    ->count();

                // 最近的风险事件
                $recentEvents = Db::name('risk_events')
                    ->order('created_at desc')
                    ->limit(10)
                    ->select();
            } catch (\Exception $e) {
                // 如果risk_events表不存在，使用默认值
                trace('risk_events表不存在或查询失败: ' . $e->getMessage(), 'info');
            }

            $data = [
                'stats' => [
                    'total_events' => $totalEvents,
                    'pending_events' => $pendingEvents,
                    'high_severity' => $highSeverityEvents,
                    'medium_severity' => $mediumSeverityEvents,
                    'audit_users' => $auditUsers,
                    'banned_users' => $bannedUsers,
                    'total_users' => $totalUsers,
                    'normal_users' => $normalUsers
                ],
                'recent_events' => $recentEvents,
                'admin_info' => $this->adminInfo
            ];

            return View::fetch('/admin/risk_dashboard', $data);

        } catch (\Exception $e) {
            abort(500, '获取风控数据失败: ' . $e->getMessage());
        }
    }

    /**
     * 风险事件列表
     */
    public function events() {
        try {
            $page = $this->request->param('page', 1);
            $pageSize = $this->request->param('page_size', 20);
            $status = $this->request->param('status', '');
            $severity = $this->request->param('severity', '');

            $where = [];
            if($status) {
                $where['status'] = $status;
            }
            if($severity) {
                $where['severity'] = $severity;
            }

            $events = Db::name('risk_events')
                ->where($where)
                ->order('created_at desc')
                ->paginate($pageSize, false, ['page' => $page]);

            $data = [
                'events' => $events,
                'status' => $status,
                'severity' => $severity,
                'admin_info' => $this->adminInfo,
                'can_manage' => $this->checkPermission(15) // 风控管理权限
            ];

            return View::fetch('/admin/risk_events', $data);

        } catch (\Exception $e) {
            abort(500, '获取风险事件失败: ' . $e->getMessage());
        }
    }

    /**
     * 处理风险事件（私有方法）
     */
    private function processEvent() {
        if(!$this->checkPermission(15)) {
            abort(403, '您没有风控管理权限');
        }

        try {
            $eventId = $this->request->param('event_id');
            $action = $this->request->param('action');
            $adminNote = $this->request->param('admin_note', '');

            if(!$eventId || !$action) {
                return json(['code' => 0, 'msg' => '参数错误']);
            }

            // 获取事件信息
            $event = Db::name('risk_events')->where('id', $eventId)->find();
            if(!$event) {
                return json(['code' => 0, 'msg' => '风险事件不存在']);
            }

            // 开始事务
            Db::startTrans();

            try {
                // 更新事件状态
                $updateData = [
                    'status' => $action == 'ignore' ? 'ignored' : 'processed',
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                Db::name('risk_events')->where('id', $eventId)->update($updateData);

                // 如果是批准操作，恢复用户状态
                if($action == 'approve' && $event['qq_numbers']) {
                    $this->restoreUsersFromAudit($event['qq_numbers'], '管理员批准: ' . $adminNote);
                }

                // 记录管理员操作日志
                $this->logAdminAction('process_risk_event', [
                    'event_id' => $eventId,
                    'action' => $action,
                    'admin_note' => $adminNote,
                    'event_type' => $event['event_type']
                ]);

                Db::commit();
                return json(['code' => 1, 'msg' => '处理成功']);

            } catch (Exception $e) {
                Db::rollback();
                throw $e;
            }

        } catch (Exception $e) {
            return json(['code' => 0, 'msg' => '处理失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 批量处理用户（私有方法）
     */
    private function batchProcessUsers() {
        if(!$this->checkPermission(15)) {
            return json(['code' => 0, 'msg' => '您没有风控管理权限']);
        }

        try {
            $qqNumbers = $this->request->param('qq_numbers');
            $action = $this->request->param('action');
            $reason = $this->request->param('reason', '');

            if(!$qqNumbers || !$action) {
                return json(['code' => 0, 'msg' => '参数错误']);
            }

            if(is_string($qqNumbers)) {
                $qqNumbers = explode(',', $qqNumbers);
            }

            // 开始事务
            Db::startTrans();

            try {
                foreach($qqNumbers as $qqNumber) {
                    $qqNumber = trim($qqNumber);
                    if(!$qqNumber) continue;

                    $user = Db::name('user')->where('uin', $qqNumber)->find();
                    if(!$user) continue;

                    $newStatus = 0; // 默认恢复正常
                    switch($action) {
                        case 'approve':
                            $newStatus = 0;
                            break;
                        case 'audit':
                            $newStatus = 3;
                            break;
                        case 'temp_ban':
                            $newStatus = 1;
                            break;
                        case 'permanent_ban':
                            $newStatus = 2;
                            break;
                    }

                    // 更新用户状态
                    Db::name('user')->where('uin', $qqNumber)->update(['status' => $newStatus]);

                    // 记录用户日志
                    Db::name('user_log')->insert([
                        'uid' => $user['uid'],
                        'type' => 'admin_action',
                        'extra' => "管理员操作: {$action}, 原因: {$reason}",
                        'time' => date('Y-m-d H:i:s')
                    ]);
                }

                // 记录管理员操作日志
                $this->logAdminAction('batch_process_users', [
                    'qq_numbers' => $qqNumbers,
                    'action' => $action,
                    'reason' => $reason
                ]);

                Db::commit();
                return json(['code' => 1, 'msg' => '批量处理成功']);

            } catch (Exception $e) {
                Db::rollback();
                throw $e;
            }

        } catch (Exception $e) {
            return json(['code' => 0, 'msg' => '批量处理失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 恢复用户状态
     */
    private function restoreUsersFromAudit($qqNumbersStr, $reason) {
        // 解析QQ号字符串
        $qqNumbersStr = trim($qqNumbersStr, '[]');
        $qqNumbers = explode(',', $qqNumbersStr);

        foreach($qqNumbers as $qqNumber) {
            $qqNumber = trim($qqNumber, ' "\'');
            if(!$qqNumber) continue;

            $user = Db::name('user')->where('uin', $qqNumber)->find();
            if($user && $user['status'] == 3) {
                // 恢复为正常状态
                Db::name('user')->where('uin', $qqNumber)->update(['status' => 0]);

                // 记录用户日志
                Db::name('user_log')->insert([
                    'uid' => $user['uid'],
                    'type' => 'risk_control_restore',
                    'extra' => $reason,
                    'time' => date('Y-m-d H:i:s')
                ]);
            }
        }
    }

    /**
     * 风控配置
     */
    public function config() {
        if(!$this->checkPermission(16)) {
            abort(403, '您没有风控配置权限');
        }

        try {
            // 从数据库读取配置
            $configData = Db::name('risk_control_config')->select();
            $config = [];

            // 转换为关联数组
            foreach($configData as $item) {
                $config[$item['config_key']] = $item['config_value'];
            }

            // 设置默认值
            if(empty($config)) {
                $config = [
                    'max_qq_per_device_per_day' => '3',
                    'max_qq_per_ip_per_day' => '3',
                    'max_devices_per_qq_per_day' => '3',
                    'max_login_attempts_per_hour' => '20',
                    'enable_new_device_check' => '1',
                    'enable_location_check' => '0',
                ];
            }

            $data = [
                'config' => $config,
                'admin_info' => $this->adminInfo
            ];

            return View::fetch('/admin/risk_config', $data);

        } catch (\Exception $e) {
            abort(500, '操作失败: ' . $e->getMessage());
        }
    }

    /**
     * 保存风控配置（私有方法）
     */
    private function saveConfig() {
        if(!$this->checkPermission(16)) {
            return json(['code' => 0, 'msg' => '您没有风控配置权限']);
        }

        try {
            // 保存配置到数据库
            $config = [
                'max_qq_per_device_per_day' => $this->request->param('max_qq_per_device_per_day', 5),
                'max_qq_per_ip_per_day' => $this->request->param('max_qq_per_ip_per_day', 10),
                'max_devices_per_qq_per_day' => $this->request->param('max_devices_per_qq_per_day', 3),
                'max_login_attempts_per_hour' => $this->request->param('max_login_attempts_per_hour', 20),
                'enable_new_device_check' => $this->request->param('enable_new_device_check', 1),
                'enable_location_check' => $this->request->param('enable_location_check', 0),
            ];

            // 保存到数据库
            foreach($config as $key => $value) {
                Db::name('risk_control_config')
                    ->where('config_key', $key)
                    ->update([
                        'config_value' => (string)$value,
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
            }

            // 记录管理员操作日志
            $this->logAdminAction('update_risk_config', $config);

            return json(['code' => 1, 'msg' => '配置保存成功']);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '操作失败: ' . $e->getMessage()]);
        }
    }





    /**
     * 记录管理员操作日志
     */
    private function logAdminAction($action, $data) {
        $logData = [
            'admin_uid' => $this->admin,
            'admin_username' => $this->adminInfo['username'],
            'action' => $action,
            'data' => json_encode($data, JSON_UNESCAPED_UNICODE),
            'ip' => $this->request->ip(),
            'created_at' => date('Y-m-d H:i:s')
        ];

        try {
            // 记录到数据库（与Go系统共享）
            Db::name('bns_useradminlog')->insert($logData);
        } catch (Exception $e) {
            // 如果数据库记录失败，记录到文件
            $logFile = \think\facade\App::getRuntimePath() . 'log/admin_risk_control.log';
            $logContent = "[" . date('Y-m-d H:i:s') . "] " . json_encode($logData, JSON_UNESCAPED_UNICODE) . "\n";
            file_put_contents($logFile, $logContent, FILE_APPEND | LOCK_EX);
        }
    }


}
